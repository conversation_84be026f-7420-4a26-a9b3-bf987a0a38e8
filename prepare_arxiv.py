#!/usr/bin/env python3
"""
ArXiv Preparation Script for Research Papers

This script prepares a research paper repository for arXiv submission following
best practices from:
- https://trevorcampbell.me/html/arxiv.html
- https://info.arxiv.org/help/faq/mistakes.html
- https://www.ianhuston.net/2011/03/checklist-for-arxiv-submission/

Usage:
    python prepare_arxiv.py [--source-dir SOURCE] [--output-dir OUTPUT] [--test-branch BRANCH]

Features:
- Creates a deep copy of the paper directory
- Flattens directory structure for arXiv compatibility
- Removes unnecessary files and development artifacts
- <PERSON>les bibliography conversion from .bib to .bbl
- Cleans LaTeX comments and unused content
- Validates compilation before packaging
- Creates submission-ready tarball

Safety:
- Works in separate directories to preserve original content
- Can test on separate git branches
- Validates all operations before making changes
"""

import os
import sys
import shutil
import subprocess
import argparse
import re
from pathlib import Path
from typing import Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class ArXivPreparer:
    """Main class for preparing papers for arXiv submission."""
    
    def __init__(self, source_dir: str, output_dir: str, test_branch: Optional[str] = None, skip_compilation: bool = False):
        self.source_dir = Path(source_dir).resolve()
        self.output_dir = Path(output_dir).resolve()
        self.test_branch = test_branch
        self.skip_compilation = skip_compilation
        self.main_tex_file = None
        
        # Files to always remove
        self.remove_patterns = [
            # LaTeX auxiliary files
            '*.aux', '*.log', '*.out', '*.blg', '*.bbl', '*.toc', '*.lof', '*.lot',
            '*.fls', '*.fdb_latexmk', '*.synctex.gz', '*.nav', '*.snm', '*.vrb',
            
            # Version control
            '.git*', '.svn*', '.hg*', '.bzr*',
            
            # Development files
            '*.pyc', '__pycache__', '.pytest_cache', '.coverage',
            '*.swp', '*.swo', '*~', '.DS_Store', 'Thumbs.db',
            
            # IDE files
            '.vscode', '.idea', '*.sublime-*',
            
            # Backup files
            '*.bak', '*.backup', '*.orig',
            
            # OS files
            '.Trash*', '.fuse_hidden*'
        ]
        
        # Extensions to keep
        self.keep_extensions = {'.tex', '.sty', '.cls', '.eps', '.ps', '.pdf', '.png', '.jpg', '.jpeg', '.svg'}
        
    def create_test_branch(self) -> bool:
        """Create and switch to test branch if specified."""
        if not self.test_branch:
            return True
            
        try:
            # Check if we're in a git repository
            result = subprocess.run(['git', 'rev-parse', '--git-dir'], 
                                  cwd=self.source_dir, capture_output=True)
            if result.returncode != 0:
                logger.warning("Not in a git repository, skipping branch creation")
                return True
                
            # Create and switch to test branch
            subprocess.run(['git', 'checkout', '-b', self.test_branch], 
                          cwd=self.source_dir, check=True)
            logger.info(f"Created and switched to test branch: {self.test_branch}")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to create test branch: {e}")
            return False
    
    def find_main_tex_file(self, directory: Path) -> Optional[Path]:
        """Find the main LaTeX file (usually main.tex or contains \\documentclass)."""
        # First, look for common main file names
        common_names = ['main.tex', 'paper.tex', 'manuscript.tex', 'article.tex']
        for name in common_names:
            candidate = directory / name
            if candidate.exists():
                return candidate
        
        # If not found, look for files containing \documentclass
        for tex_file in directory.glob('*.tex'):
            try:
                with open(tex_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    if '\\documentclass' in content:
                        return tex_file
            except Exception:
                continue
                
        return None
    
    def deep_copy_source(self) -> bool:
        """Create a deep copy of the source directory."""
        try:
            if self.output_dir.exists():
                logger.info(f"Removing existing output directory: {self.output_dir}")
                shutil.rmtree(self.output_dir)
            
            logger.info(f"Creating deep copy: {self.source_dir} -> {self.output_dir}")
            shutil.copytree(self.source_dir, self.output_dir)
            
            # Find main tex file
            self.main_tex_file = self.find_main_tex_file(self.output_dir)
            if not self.main_tex_file:
                logger.error("Could not find main LaTeX file")
                return False
                
            logger.info(f"Found main LaTeX file: {self.main_tex_file.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create deep copy: {e}")
            return False
    
    def flatten_directory_structure(self) -> bool:
        """Flatten subdirectories and move files to root."""
        try:
            logger.info("Flattening directory structure...")
            
            # Find all files in subdirectories
            files_to_move = []
            for root, dirs, files in os.walk(self.output_dir):
                root_path = Path(root)
                if root_path == self.output_dir:
                    continue  # Skip root directory
                    
                for file in files:
                    file_path = root_path / file
                    if file_path.suffix.lower() in self.keep_extensions:
                        files_to_move.append(file_path)
            
            # Move files to root and update references
            moved_files = {}
            for file_path in files_to_move:
                relative_path = file_path.relative_to(self.output_dir)
                new_name = str(relative_path).replace(os.sep, '_')
                new_path = self.output_dir / new_name
                
                # Handle name conflicts
                counter = 1
                while new_path.exists():
                    name_parts = new_name.rsplit('.', 1)
                    if len(name_parts) == 2:
                        new_name = f"{name_parts[0]}_{counter}.{name_parts[1]}"
                    else:
                        new_name = f"{new_name}_{counter}"
                    new_path = self.output_dir / new_name
                    counter += 1
                
                shutil.move(str(file_path), str(new_path))
                moved_files[str(relative_path)] = new_name
                logger.info(f"Moved: {relative_path} -> {new_name}")
            
            # Update file references in .tex files
            self.update_file_references(moved_files)
            
            # Remove empty subdirectories
            for root, dirs, files in os.walk(self.output_dir, topdown=False):
                for dir_name in dirs:
                    dir_path = Path(root) / dir_name
                    try:
                        if not any(dir_path.iterdir()):
                            dir_path.rmdir()
                            logger.info(f"Removed empty directory: {dir_path.relative_to(self.output_dir)}")
                    except OSError:
                        pass  # Directory not empty or other error
            
            return True

        except Exception as e:
            logger.error(f"Failed to flatten directory structure: {e}")
            return False

    def update_file_references(self, moved_files: dict) -> None:
        """Update file references in LaTeX files after flattening."""
        tex_files = list(self.output_dir.glob('*.tex'))

        for tex_file in tex_files:
            try:
                with open(tex_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                original_content = content

                # Update file references
                for old_path, new_name in moved_files.items():
                    # Handle different LaTeX commands that reference files
                    # Remove file extension for input/include commands
                    old_path_no_ext = old_path
                    if old_path_no_ext.endswith('.tex'):
                        old_path_no_ext = old_path_no_ext[:-4]

                    new_name_no_ext = new_name
                    if new_name_no_ext.endswith('.tex'):
                        new_name_no_ext = new_name_no_ext[:-4]

                    # Update references with and without extensions
                    replacements = [
                        (old_path, new_name),  # Full path with extension
                        (old_path_no_ext, new_name_no_ext),  # Path without extension
                    ]

                    for old_ref, new_ref in replacements:
                        # More robust LaTeX command replacement
                        # Handle \input{}, \include{}, \includegraphics{}, \includesvg{}, etc.
                        latex_commands = [
                            r'\\input\{' + re.escape(old_ref) + r'\}',
                            r'\\include\{' + re.escape(old_ref) + r'\}',
                            r'\\includegraphics(?:\[[^\]]*\])?\{' + re.escape(old_ref) + r'\}',
                            r'\\includesvg(?:\[[^\]]*\])?\{' + re.escape(old_ref) + r'\}',
                        ]

                        for pattern in latex_commands:
                            # Replace the command while preserving the command structure
                            if r'\\includegraphics' in pattern:
                                content = re.sub(pattern, lambda m: m.group(0).replace(old_ref, new_ref), content)
                            elif r'\\includesvg' in pattern:
                                content = re.sub(pattern, lambda m: m.group(0).replace(old_ref, new_ref), content)
                            else:
                                content = re.sub(pattern, lambda m: m.group(0).replace(old_ref, new_ref), content)

                        # Also do simple string replacement as fallback
                        content = content.replace(f'{{{old_ref}}}', f'{{{new_ref}}}')

                # Write back if changed
                if content != original_content:
                    with open(tex_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    logger.info(f"Updated file references in: {tex_file.name}")

            except Exception as e:
                logger.warning(f"Failed to update references in {tex_file}: {e}")

    def remove_unnecessary_files(self) -> bool:
        """Remove unnecessary files and development artifacts."""
        try:
            logger.info("Removing unnecessary files...")

            removed_count = 0
            for pattern in self.remove_patterns:
                for file_path in self.output_dir.rglob(pattern):
                    if file_path.is_file():
                        file_path.unlink()
                        removed_count += 1
                        logger.debug(f"Removed: {file_path.relative_to(self.output_dir)}")
                    elif file_path.is_dir():
                        shutil.rmtree(file_path)
                        removed_count += 1
                        logger.debug(f"Removed directory: {file_path.relative_to(self.output_dir)}")

            logger.info(f"Removed {removed_count} unnecessary files/directories")
            return True

        except Exception as e:
            logger.error(f"Failed to remove unnecessary files: {e}")
            return False

    def clean_latex_comments(self) -> bool:
        """Remove LaTeX comments from .tex files."""
        try:
            logger.info("Cleaning LaTeX comments...")

            tex_files = list(self.output_dir.glob('*.tex'))
            for tex_file in tex_files:
                with open(tex_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                cleaned_lines = []
                for line in lines:
                    # Remove comments but preserve escaped % characters
                    # This is a simple approach - more sophisticated parsing might be needed
                    if '\\%' in line:
                        # Handle escaped % characters
                        parts = line.split('\\%')
                        cleaned_parts = []
                        for i, part in enumerate(parts):
                            if i == 0:
                                # First part - remove comments normally
                                comment_pos = part.find('%')
                                if comment_pos >= 0:
                                    part = part[:comment_pos]
                            cleaned_parts.append(part)
                        line = '\\%'.join(cleaned_parts)
                    else:
                        # Simple comment removal
                        comment_pos = line.find('%')
                        if comment_pos >= 0:
                            line = line[:comment_pos]

                    # Keep line if it has content or is needed for spacing
                    if line.strip() or not cleaned_lines or cleaned_lines[-1].strip():
                        cleaned_lines.append(line.rstrip() + '\n')

                # Write back cleaned content
                with open(tex_file, 'w', encoding='utf-8') as f:
                    f.writelines(cleaned_lines)

                logger.info(f"Cleaned comments from: {tex_file.name}")

            return True

        except Exception as e:
            logger.error(f"Failed to clean LaTeX comments: {e}")
            return False

    def handle_bibliography(self) -> bool:
        """Handle bibliography - use existing .bbl file or compile if needed."""
        try:
            logger.info("Handling bibliography...")

            # Check if there's already a .bbl file in the source directory
            source_bbl = self.source_dir / f"{self.main_tex_file.stem}.bbl"
            target_bbl = self.main_tex_file.with_suffix('.bbl')

            if source_bbl.exists() and not target_bbl.exists():
                logger.info(f"Found existing .bbl file, copying: {source_bbl.name}")
                shutil.copy2(source_bbl, target_bbl)

            # If we have a .bbl file, use it directly
            if target_bbl.exists():
                logger.info("Using existing .bbl file for bibliography")

                # Read .bbl content
                with open(target_bbl, 'r', encoding='utf-8') as f:
                    bbl_content = f.read()

                # Read main .tex file
                with open(self.main_tex_file, 'r', encoding='utf-8') as f:
                    tex_content = f.read()

                # Replace \bibliography and \bibliographystyle commands with .bbl content
                # Remove \bibliographystyle{...}
                tex_content = re.sub(r'\\bibliographystyle\{[^}]*\}', '', tex_content)

                # Replace \bibliography{...} with .bbl content
                # Use lambda to avoid regex escape sequence issues with backslashes in bbl_content
                tex_content = re.sub(r'\\bibliography\{[^}]*\}', lambda _: bbl_content, tex_content)

                # Write back modified .tex file
                with open(self.main_tex_file, 'w', encoding='utf-8') as f:
                    f.write(tex_content)

                # Remove .bib files
                for bib_file in self.output_dir.glob('*.bib'):
                    bib_file.unlink()
                    logger.info(f"Removed .bib file: {bib_file.name}")

                logger.info("Bibliography handling completed using existing .bbl file")
                return True

            # If no .bbl file exists, try to compile
            if not self.check_latex_available():
                logger.warning("LaTeX not available and no .bbl file found")
                logger.info("Note: You will need to compile and handle bibliography manually")
                logger.info("Tip: Run 'pdflatex main.tex && bibtex main && pdflatex main.tex' in your source directory first")

                # Still remove .bib files to clean up
                for bib_file in self.output_dir.glob('*.bib'):
                    bib_file.unlink()
                    logger.info(f"Removed .bib file: {bib_file.name}")

                return True

            # Try to compile the document to generate .bbl file
            logger.info("Attempting to compile LaTeX to generate .bbl file...")
            if not self.compile_latex(generate_bbl=True):
                logger.warning("Failed to compile LaTeX for bibliography generation")
                logger.info("This is often due to missing LaTeX packages or TikZ compilation issues")
                logger.info("Recommendation: Compile your document locally first to generate the .bbl file")

                # Don't fail completely - just skip bibliography processing
                return True

            # Check if .bbl file was generated
            if not target_bbl.exists():
                logger.warning("No .bbl file found after compilation - document may not have bibliography")
                return True

            # Process the generated .bbl file (same as above)
            with open(target_bbl, 'r', encoding='utf-8') as f:
                bbl_content = f.read()

            with open(self.main_tex_file, 'r', encoding='utf-8') as f:
                tex_content = f.read()

            tex_content = re.sub(r'\\bibliographystyle\{[^}]*\}', '', tex_content)
            tex_content = re.sub(r'\\bibliography\{[^}]*\}', lambda _: bbl_content, tex_content)

            with open(self.main_tex_file, 'w', encoding='utf-8') as f:
                f.write(tex_content)

            for bib_file in self.output_dir.glob('*.bib'):
                bib_file.unlink()
                logger.info(f"Removed .bib file: {bib_file.name}")

            logger.info("Bibliography handling completed")
            return True

        except Exception as e:
            logger.error(f"Failed to handle bibliography: {e}")
            return False

    def add_arxiv_compilation_hint(self) -> bool:
        """Add compilation hint for arXiv to ensure multiple passes."""
        try:
            with open(self.main_tex_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Add the hint after \end{document}
            hint = "\n\\typeout{get arXiv to do 4 passes: Label(s) may have changed. Rerun}\n"

            if '\\end{document}' in content and hint not in content:
                content = content.replace('\\end{document}', '\\end{document}' + hint)

                with open(self.main_tex_file, 'w', encoding='utf-8') as f:
                    f.write(content)

                logger.info("Added arXiv compilation hint")

            return True

        except Exception as e:
            logger.error(f"Failed to add arXiv compilation hint: {e}")
            return False

    def check_latex_available(self) -> bool:
        """Check if LaTeX tools are available."""
        try:
            subprocess.run(['pdflatex', '--version'], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False

    def compile_latex(self, generate_bbl: bool = False) -> bool:
        """Compile LaTeX document to verify it works."""
        if self.skip_compilation:
            logger.info("Skipping LaTeX compilation (--skip-compilation flag)")
            return True

        if not self.check_latex_available():
            logger.warning("LaTeX not available - skipping compilation")
            return True

        try:
            logger.info("Compiling LaTeX document...")

            # Change to output directory
            original_cwd = os.getcwd()
            os.chdir(self.output_dir)

            try:
                # Run pdflatex
                cmd = ['pdflatex', '-interaction=nonstopmode', self.main_tex_file.name]
                result = subprocess.run(cmd, capture_output=True, text=True)

                if result.returncode != 0:
                    logger.error(f"pdflatex failed:\n{result.stdout}\n{result.stderr}")
                    return False

                if generate_bbl:
                    # Run bibtex if .aux file exists and contains citations
                    aux_file = self.main_tex_file.with_suffix('.aux')
                    if aux_file.exists():
                        with open(aux_file, 'r') as f:
                            aux_content = f.read()

                        if '\\bibdata' in aux_content or '\\citation' in aux_content:
                            logger.info("Running bibtex...")
                            cmd = ['bibtex', self.main_tex_file.stem]
                            subprocess.run(cmd, capture_output=True)

                            # Run pdflatex again to incorporate bibliography
                            subprocess.run(['pdflatex', '-interaction=nonstopmode',
                                          self.main_tex_file.name], capture_output=True)
                            subprocess.run(['pdflatex', '-interaction=nonstopmode',
                                          self.main_tex_file.name], capture_output=True)

                logger.info("LaTeX compilation successful")
                return True

            finally:
                os.chdir(original_cwd)

        except Exception as e:
            logger.error(f"Failed to compile LaTeX: {e}")
            return False

    def validate_submission(self) -> bool:
        """Perform final validation checks."""
        try:
            logger.info("Performing final validation...")

            # Check for absolute paths
            tex_files = list(self.output_dir.glob('*.tex'))
            for tex_file in tex_files:
                with open(tex_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Look for absolute paths
                if re.search(r'[/\\][a-zA-Z]', content):
                    logger.warning(f"Possible absolute path found in {tex_file.name}")

            # Check for spaces in filenames
            for file_path in self.output_dir.iterdir():
                if ' ' in file_path.name:
                    logger.warning(f"File with spaces in name: {file_path.name}")

            # Check for required files
            if not self.main_tex_file.exists():
                logger.error("Main LaTeX file missing")
                return False

            # Check for .bbl file if bibliography was used
            bbl_file = self.main_tex_file.with_suffix('.bbl')
            if bbl_file.exists():
                logger.info("Bibliography (.bbl) file found")

            # Final compilation test (optional - don't fail if it doesn't work)
            if not self.compile_latex():
                logger.warning("Final compilation test failed")
                logger.info("This may be due to missing LaTeX packages or local environment differences")
                logger.info("The files have been prepared for arXiv - you can test compilation manually")

            logger.info("Validation completed successfully")
            return True

        except Exception as e:
            logger.error(f"Validation failed: {e}")
            return False

    def clean_compilation_artifacts(self) -> bool:
        """Remove compilation artifacts but keep .bbl file."""
        try:
            logger.info("Cleaning compilation artifacts...")

            artifacts = ['*.aux', '*.log', '*.out', '*.pdf', '*.fls', '*.fdb_latexmk',
                        '*.synctex.gz', '*.toc', '*.lof', '*.lot']

            removed_count = 0
            for pattern in artifacts:
                for file_path in self.output_dir.glob(pattern):
                    # Keep .bbl files
                    if file_path.suffix == '.bbl':
                        continue
                    file_path.unlink()
                    removed_count += 1

            logger.info(f"Removed {removed_count} compilation artifacts")
            return True

        except Exception as e:
            logger.error(f"Failed to clean compilation artifacts: {e}")
            return False

    def create_tarball(self) -> bool:
        """Create submission tarball."""
        try:
            logger.info("Creating submission tarball...")

            tarball_path = self.output_dir.parent / f"{self.output_dir.name}.tar"

            # Change to parent directory for relative paths in tarball
            original_cwd = os.getcwd()
            os.chdir(self.output_dir.parent)

            try:
                cmd = ['tar', '-cvf', str(tarball_path), self.output_dir.name]
                result = subprocess.run(cmd, capture_output=True, text=True)

                if result.returncode != 0:
                    logger.error(f"tar failed: {result.stderr}")
                    return False

                logger.info(f"Created tarball: {tarball_path}")
                logger.info(f"Tarball size: {tarball_path.stat().st_size / 1024:.1f} KB")

                return True

            finally:
                os.chdir(original_cwd)

        except Exception as e:
            logger.error(f"Failed to create tarball: {e}")
            return False

    def run(self) -> bool:
        """Execute the complete arXiv preparation process."""
        logger.info("Starting arXiv preparation process...")

        steps = [
            ("Creating test branch", self.create_test_branch),
            ("Creating deep copy", self.deep_copy_source),
            ("Flattening directory structure", self.flatten_directory_structure),
            ("Removing unnecessary files", self.remove_unnecessary_files),
            ("Cleaning LaTeX comments", self.clean_latex_comments),
            ("Handling bibliography", self.handle_bibliography),
            ("Adding arXiv compilation hint", self.add_arxiv_compilation_hint),
            ("Validating submission", self.validate_submission),
            ("Cleaning compilation artifacts", self.clean_compilation_artifacts),
            ("Creating tarball", self.create_tarball),
        ]

        for step_name, step_func in steps:
            logger.info(f"Step: {step_name}")
            if not step_func():
                logger.error(f"Failed at step: {step_name}")
                return False

        logger.info("arXiv preparation completed successfully!")
        logger.info(f"Submission files ready in: {self.output_dir}")
        logger.info(f"Tarball created: {self.output_dir.parent / f'{self.output_dir.name}.tar'}")

        return True


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(
        description="Prepare research paper for arXiv submission",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage - prepare current directory
  python prepare_arxiv.py

  # Specify source and output directories
  python prepare_arxiv.py --source-dir ./paper --output-dir ./arxiv_submission

  # Test on a separate git branch
  python prepare_arxiv.py --test-branch arxiv-prep-test

  # Verbose output
  python prepare_arxiv.py --verbose

Safety Features:
  - Creates deep copy of source directory
  - Can test on separate git branch
  - Validates all operations before making changes
  - Preserves original content

Following Best Practices From:
  - Trevor Campbell's arXiv guide
  - arXiv FAQ on common mistakes
  - Ian Huston's submission checklist
        """
    )

    parser.add_argument(
        '--source-dir', '-s',
        default='.',
        help='Source directory containing the paper (default: current directory)'
    )

    parser.add_argument(
        '--output-dir', '-o',
        default='arxiv_submission',
        help='Output directory for prepared submission (default: arxiv_submission)'
    )

    parser.add_argument(
        '--test-branch', '-b',
        help='Create and use a test git branch for preparation'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be done without making changes'
    )

    parser.add_argument(
        '--skip-compilation',
        action='store_true',
        help='Skip LaTeX compilation steps (useful when local environment differs from arXiv)'
    )

    args = parser.parse_args()

    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Validate source directory
    source_path = Path(args.source_dir).resolve()
    if not source_path.exists():
        logger.error(f"Source directory does not exist: {source_path}")
        return 1

    if not source_path.is_dir():
        logger.error(f"Source path is not a directory: {source_path}")
        return 1

    # Check for LaTeX files
    tex_files = list(source_path.glob('*.tex'))
    if not tex_files:
        logger.error(f"No LaTeX files found in source directory: {source_path}")
        return 1

    if args.dry_run:
        logger.info("DRY RUN MODE - No changes will be made")
        logger.info(f"Would prepare: {source_path}")
        logger.info(f"Would output to: {Path(args.output_dir).resolve()}")
        if args.test_branch:
            logger.info(f"Would create test branch: {args.test_branch}")
        return 0

    # Create preparer and run
    preparer = ArXivPreparer(
        source_dir=args.source_dir,
        output_dir=args.output_dir,
        test_branch=args.test_branch,
        skip_compilation=args.skip_compilation
    )

    try:
        success = preparer.run()
        return 0 if success else 1

    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
