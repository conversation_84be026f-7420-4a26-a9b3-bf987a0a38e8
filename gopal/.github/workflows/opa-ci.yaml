name: OPA Policies CI

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
  workflow_dispatch:

jobs:
  opa-checks:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Install OPA
        run: |
          curl -L -o opa https://openpolicyagent.org/downloads/latest/opa_linux_amd64
          chmod 755 opa
          sudo mv opa /usr/local/bin/
          opa version

      - name: Install Regal
        run: |
          curl -L -o regal https://github.com/StyraInc/regal/releases/latest/download/regal_Linux_x86_64
          chmod +x regal
          sudo mv regal /usr/local/bin/
          regal version

      - name: Run OPA Check
        run: opa check .
        working-directory: ${{ github.workspace }}

      - name: Run Regal Lint
        run: regal lint .
        working-directory: ${{ github.workspace }}
