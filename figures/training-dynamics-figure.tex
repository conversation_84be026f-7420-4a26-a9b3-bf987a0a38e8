% training-dynamics-figure.tex
% Modular 4-subplot training dynamics figure using pgfplots
% This replaces the previous PDF-based subplot figure with clean LaTeX plots

% Include the configuration
\input{figures/pgfplots-config}

\begin{figure}[ht]
\centering

% Top row: Combined Reward and Training Loss
\begin{subfigure}[b]{0.49\textwidth}
    \centering
    \begin{tikzpicture}
        \plotCombinedReward
    \end{tikzpicture}
    \label{fig:training-dynamics-reward}
\end{subfigure}
\hfill
\begin{subfigure}[b]{0.49\textwidth}
    \centering
    \begin{tikzpicture}
        \plotTrainingLoss
    \end{tikzpicture}
    \label{fig:training-dynamics-loss}
\end{subfigure}

\vspace{1em}

% Bottom row: KL Divergence and Reward Standard Deviation
\begin{subfigure}[b]{0.49\textwidth}
    \centering
    \begin{tikzpicture}
         \plotKLDivergence
    \end{tikzpicture}
    \label{fig:training-dynamics-kl}
\end{subfigure}
\hfill
\begin{subfigure}[b]{0.49\textwidth}
    \centering
    \begin{tikzpicture}
        \plotRewardStdDev
    \end{tikzpicture}
    \label{fig:training-dynamics-std}
\end{subfigure}

\caption{Training dynamics across 3 epochs showing convergence patterns. All metrics are exponentially smoothed (EMA with $\alpha=0.08$) for clarity. The combined reward shows steady improvement, while training loss decreases and stabilizes. KL divergence remains controlled, and reward standard deviation indicates consistent policy behavior.}
\label{fig:training-dynamics}
\end{figure}
