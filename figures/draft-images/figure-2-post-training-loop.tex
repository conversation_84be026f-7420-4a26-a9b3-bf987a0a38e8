% Second TikZ diagram: Post-Training Continuous-Alignment Loop
% This diagram shows the runtime flow of the ArGen framework for
% the post-training lifecycle and continuous alignment process.

\usetikzlibrary{arrows.meta, arrows, positioning, shapes, calc, fit, shapes.geometric}

% Define colors consistent with the first diagram
\definecolor{boxStroke}{HTML}{CBD5E0}
\definecolor{centralFill}{HTML}{EBF8FF}
\definecolor{centralStroke}{HTML}{4299E1}
\definecolor{outputFill}{HTML}{F0FFF4}
\definecolor{outputStroke}{HTML}{48BB78}
\definecolor{arrowLine}{HTML}{A0AEC0}
\definecolor{cycleArrowLine}{HTML}{718096}
\definecolor{outputArrowLine}{HTML}{68D391}
\definecolor{mainLabel}{HTML}{2D3748}
\definecolor{subLabel}{HTML}{4A5568}
\definecolor{titleColor}{HTML}{1A202C}
% New colors for this diagram
\definecolor{orangeFill}{HTML}{FFEDD5}
\definecolor{orangeStroke}{HTML}{F97316}
\definecolor{redStroke}{HTML}{EF4444}
\definecolor{greyFill}{HTML}{F3F4F6}
\definecolor{greyStroke}{HTML}{6B7280}

\begin{tikzpicture}[
    font=\sffamily,
    node distance=1cm and 1.5cm,
    % --- NODE STYLES ---
    base_box/.style={
        draw=boxStroke,
        fill=white,
        thick,
        rounded corners=8pt,
        text width=5.5cm,
        minimum height=2cm,
        align=center,
        text=mainLabel
    },
    central_box/.style={
        base_box,
        draw=centralStroke,
        fill=centralFill,
    },
    green_box/.style={
        base_box,
        draw=outputStroke,
        fill=outputFill,
        text width=6cm,
    },
    orange_box/.style={
        base_box,
        draw=orangeStroke,
        fill=orangeFill,
    },
    red_outline_box/.style={
        base_box,
        draw=redStroke,
        thick,
        text=redStroke,
    },
    grey_box/.style={
        base_box,
        draw=greyStroke,
        fill=greyFill,
    },
    dotted_blue_box/.style={
        base_box,
        draw=centralStroke,
        dashed,
        text=centralStroke,
    },
    dispatcher_diamond/.style={
        shape=diamond,
        draw=outputStroke,
        fill=outputFill,
        thick,
        minimum size=1.8cm,
        text=mainLabel,
        font=\small,
        align=center
    },
    container_box/.style={
        draw,
        rounded corners=12pt,
        inner sep=0.75cm,
    },
    % --- ARROW STYLES ---
    arrow/.style={ ->, >=stealth', draw=arrowLine, thick },
    dashed_arrow/.style={ ->, >=stealth', draw=cycleArrowLine, thick, dashed, dash pattern=on 4pt off 3pt },
    dotted_arrow/.style={ ->, >=stealth', draw=cycleArrowLine, thick, dotted },
    green_arrow/.style={ ->, >=stealth', draw=outputStroke, thick },
    red_arrow/.style={ ->, >=stealth', draw=redStroke, thick }
]
    % === NODES ===
    
    % Core Process Column (Right side of the diagram) - Shifted further right
    \node[base_box, text width=5cm] (user_prompt) at (8, 0) {\bfseries User Prompt / API Call \\ \textcolor{subLabel}{"End-user prompt or upstream service request"}};
    \node[central_box, below=of user_prompt] (llm) {\bfseries Policy Model (LLM) \\ \textcolor{subLabel}{ArGen-aligned LLM (frozen weights)}};
    \node[dispatcher_diamond, below=1.5cm of llm] (dispatcher) {Response\\Dispatcher};
    \node[green_box, below=1.5cm of dispatcher] (telemetry) {\bfseries Telemetry \& Drift Logs \\ \textcolor{subLabel}{Reward sub-scores + policy verdicts $\rightarrow$ Monitoring DB}};

    % Policy & Ops Column (Left side of the diagram)
    \node[base_box] (policy_repo) at (0, 0) {\bfseries Policy Repo \\ \textcolor{subLabel}{Rego / Python rule library}};
    \node[grey_box, below=of policy_repo] (ci_cd_ops) {\bfseries Policy \& Weight Ops (CI/CD) \\ \textcolor{subLabel}{Git commit $\rightarrow$ CI $\rightarrow$ Hot-swap overlay (<60 s)}};
    \node[orange_box, below=of ci_cd_ops] (policy_overlay) {\bfseries Python / OPA Policy Overlay \\ \textcolor{subLabel}{Live Policy-as-Code Overlay \\ Executes scope \& safety checks (P\textsubscript{scope}, P\textsubscript{sev})}};
    \node[red_outline_box, below=of policy_overlay] (violation_handler) {\bfseries Policy Violation Handler \\ \textcolor{subLabel}{Block | Redact | Rewrite}};

    % Optional fine-tuning block
    \node[dotted_blue_box, right=of dispatcher] (fine_tune) {\bfseries Incremental Fine-tune (optional) \\ \textcolor{subLabel}{Nightly micro-GRPO on violation traces}};
    
    % === CONTAINERS and LABELS ===
    \node[container_box, draw=blue!50, fit=(user_prompt) (llm) (dispatcher) (telemetry) (fine_tune)] (core_container) {};
    \node[above=0.2cm of core_container.north, font=\sffamily] {Core Process};

    \node[container_box, draw=red!50, fit=(policy_repo) (ci_cd_ops) (policy_overlay) (violation_handler)] (ops_container) {};
    \node[above=0.2cm of ops_container.north, font=\sffamily] {Policy \& Ops};

    % === ARROWS and FLOW ===
    % Increased font size for arrow labels to \footnotesize
    
    \draw[arrow] (user_prompt) -- (llm);
    \draw[arrow] (llm) -- (policy_overlay);
    
    % Flow from Policy Overlay
    \draw[green_arrow] (policy_overlay) -- node[midway, above, font=\footnotesize] {Pass} (dispatcher);
    \draw[red_arrow] (policy_overlay) -- node[midway, left, font=\footnotesize] {Violation} (violation_handler);
    
    % Violation handler loop
    \draw[red_arrow] (violation_handler) -- node[midway, above, font=\footnotesize] {Rewrite} (dispatcher);
    
    % Dispatcher outcomes - Flipped to bend right & label repositioned
    \draw[green_arrow] (dispatcher) to[bend right=45] node[pos=0.6, midway, yshift=-20pt] {Approved} (user_prompt);
    \draw[arrow] (dispatcher) -- (telemetry);
    \draw[arrow] (violation_handler.south) |- ($(telemetry.west) - (0.5,0)$) -> (telemetry.west);
    
    % Right-hand side Ops flow
    \draw[arrow] (policy_repo) -- (ci_cd_ops);
    \draw[arrow] (ci_cd_ops) -- (policy_overlay);
    
    % Bottom Telemetry and Fine-tuning flow - Flipped to bend right & label repositioned
    \draw[dotted_arrow] (telemetry) -- (fine_tune);
    \draw[dashed_arrow] (fine_tune) to[bend right=45] node[pos=0.6, midway, yshift=-30pt] {Updated Checkpoint} (llm);

\end{tikzpicture}
