% First TikZ diagram: The ArGen Framework for Auto-Regulatory AI Alignment
% This diagram shows the high-level conceptual framework

\usetikzlibrary{arrows.meta, arrows, positioning, shapes, calc, fit, shapes.geometric}

% Define colors from the SVG for accuracy
\definecolor{boxStroke}{HTML}{CBD5E0}
\definecolor{centralFill}{HTML}{EBF8FF}
\definecolor{centralStroke}{HTML}{4299E1}
\definecolor{outputFill}{HTML}{F0FFF4}
\definecolor{outputStroke}{HTML}{48BB78}
\definecolor{arrowLine}{HTML}{A0AEC0}
\definecolor{cycleArrowLine}{HTML}{718096}
\definecolor{outputArrowLine}{HTML}{68D391}
\definecolor{mainLabel}{HTML}{2D3748}
\definecolor{subLabel}{HTML}{4A5568}
\definecolor{titleColor}{HTML}{1A202C}

\begin{tikzpicture}[
    font=\sffamily,
    % General style for all primary boxes
    base_box/.style={
        draw=boxStroke,
        fill=white,
        thick,
        rounded corners=8pt,
        text width=6cm,
        minimum height=2.5cm,
        align=center,
        text=mainLabel
    },
    % Style for the central policy model
    central_box/.style={
        base_box,
        draw=centralStroke,
        fill=centralFill,
        text width=8cm
    },
    % Style for the final output box
    output_box/.style={
        base_box,
        draw=outputStroke,
        fill=outputFill,
        minimum height=2cm
    },
    % Style for the smaller "icon" boxes at the top
    icon_box/.style={
        draw=boxStroke,
        fill=gray!5,
        thick,
        rounded corners=4pt,
        minimum width=3.5cm,
        minimum height=1.5cm,
        align=center,
        font=\small
    },
    % --- Arrow Styles ---
    % Standard arrow style (using 'arrows' library syntax)
    arrow/.style={
        ->, >=stealth', 
        draw=arrowLine,
        thick
    },
    % Dashed arrow for the regulatory cycle
    cycle_arrow/.style={
        ->, >=stealth',
        draw=cycleArrowLine,
        thick,
        dashed,
        dash pattern=on 4pt off 3pt
    },
    % Green arrow for the final output
    output_arrow/.style={
        ->, >=stealth',
        draw=outputArrowLine,
        thick
    }
]

    % === NODES ===
    % Defines all the shapes and text blocks in the diagram.

    % Main Title
    \node[font=\Large\bfseries, text=titleColor] (title) at (0, 7.5) {The ArGen Framework for Auto-Regulatory AI Alignment};

    % Central Policy Model
    \node[central_box] (policy_model) at (0, 0) {
        \bfseries Policy Model \\ \textcolor{subLabel}{(LLM)}
    };

    % Input Nodes (Left and Right)
    \node[base_box, text width=5.5cm, left=2.5cm of policy_model] (reward_system) {
        \bfseries Automated Reward Scoring \\ \textcolor{subLabel}{(LLM-as-a-Judge)}
    };
    \node[base_box, text width=5.5cm, right=2.5cm of policy_model] (rl_engine) {
        \bfseries Policy Optimisation \\ \textcolor{subLabel}{(GRPO)}
    };

    % Final Output Node
    \node[output_box, below=2cm of policy_model] (output) {
        \bfseries\color{outputStroke} Aligned and Governed Response
    };

    % Top Section: Policies and Principles (moved higher for clearance)
    \node[font=\bfseries, text=mainLabel, above=4cm of policy_model] (policies_title) {Configurable Policies and Principles};
    
    % The three smaller boxes for different policies
    \node[icon_box, below=0.5cm of policies_title] (regulatory) {Regulatory Rules};
    \node[icon_box, left=0.2cm of regulatory] (ethical) {Ethical Principles};
    \node[icon_box, right=0.2cm of regulatory] (operational) {Operational Policies};

    % Container to group the three policy boxes
    \node[draw=boxStroke, rounded corners=8pt, dashed, inner sep=10pt, fit=(ethical) (regulatory) (operational)] (policy_container) {};


    % === ARROWS ===
    % Draws the connections between the nodes.

    % Arrows now originate from the policy container node
    \draw[arrow] (policy_container.south) -- (policy_model.north);
    \draw[arrow] (policy_container.west) to[out=-180, in=90, looseness=0.8] (reward_system.north);

    % Arrow from the central model to the final output
    \draw[output_arrow] (policy_model.south) -- (output.north);

    % The Auto-Regulatory Learning Cycle arrows
    \draw[cycle_arrow] (policy_model.west) -- (reward_system.east);
    \draw[cycle_arrow] (rl_engine.west) -- (policy_model.east);
    
    % The large, curved arrow at the bottom, including its label
    \draw[cycle_arrow] (reward_system.south) to[out=-90, in=-90, looseness=0.8] 
        node[pos=0.5, below=0.3cm, font=\small, text=cycleArrowLine] {Auto-Regulatory Learning Cycle} 
        (rl_engine.south);

\end{tikzpicture}

