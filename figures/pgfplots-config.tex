% pgfplots-config.tex
% Modular configuration for training dynamics plots
% This file contains shared styling and configuration for all training plots

% Common plot styling
\pgfplotsset{
    training-plot/.style={
        width=\textwidth,
        height=0.7\textwidth,
        grid=major,
        grid style={dashed,gray!40},
        tick label style={font=\small},
        label style={font=\normalsize},
        legend style={font=\small},
        line width=1.5pt,
        mark size=1.0pt,
        every axis plot/.append style={smooth},
        axis lines=left,
        xlabel style={yshift=-3pt},
        ylabel style={yshift=3pt},
        legend pos=south east,
        legend cell align=left,
    },
    % Color scheme for consistency
    reward-color/.style={color=blue!70!black},
    loss-color/.style={color=black},
    kl-color/.style={color=black},
    std-color/.style={color=black},
}

% Axis formatting helpers
\newcommand{\trainingxaxis}{
    xlabel={Training Epoch},
    xmin=0, xmax=3,
    xtick={0,0.5,1.0,1.5,2.0,2.5,3.0},
    xticklabels={0,0.5,1.0,1.5,2.0,2.5,3.0},
}

% Individual plot commands for modularity
\newcommand{\plotCombinedReward}{
    \begin{axis}[
        training-plot,
        \trainingxaxis,
        ymin=0.66, ymax=0.84,
        ytick={0.66,0.70,0.74,0.78,0.82},
        title={Combined Reward},
        title style={font=\small\bfseries},
    ]
    \addplot[reward-color, no marks] table[
        x=epoch,
        y=value,
        col sep=comma
    ] {figures/CombinedRewards_ema.csv};
    \end{axis}
}

\newcommand{\plotTrainingLoss}{
    \begin{axis}[
        training-plot,
        \trainingxaxis,
        ymin=0.0, ymax=0.1,
        ytick={0.0,0.02,0.04,0.06,0.08,0.10},
        title={Training Loss},
        title style={font=\small\bfseries},
    ]
    \addplot[loss-color, no marks] table[
        x=epoch,
        y=value,
        col sep=comma
    ] {figures/TrainingLoss_ema.csv};
    \end{axis}
}

\newcommand{\plotKLDivergence}{
    \begin{axis}[
        training-plot,
        \trainingxaxis,
        ymin=0, ymax=1,
        ytick={0,0.2,0.4,0.6,0.8,1.0},
        title={KL Divergence},
        title style={font=\small\bfseries},
    ]
    \addplot[kl-color, no marks] table[
        x=epoch,
        y=value,
        col sep=comma
    ] {figures/KLDivergence_ema.csv};
    \end{axis}
}

\newcommand{\plotRewardStdDev}{
    \begin{axis}[
        training-plot,
        \trainingxaxis,
        ymin=0.05, ymax=0.25,
        ytick={0.05,0.10,0.15,0.20,0.25},
        title={Reward Standard Deviation},
        title style={font=\small\bfseries},
    ]
    \addplot[std-color, no marks] table[
        x=epoch,
        y=value,
        col sep=comma
    ] {figures/RewardStdDev_ema.csv};
    \end{axis}
}
