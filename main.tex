% ------------ Class Definition -------------------
% The 'preprint' option is correct for arXiv. It enables numbered sections.
\documentclass{article}
\usepackage[para]{footmisc}
\usepackage[preprint]{neurips_2024}

% ------------ Preamble ---------------------------
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{lmodern,microtype}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx,booktabs,url,xcolor,multirow}
\usepackage{adjustbox}
\usepackage{tikz}
\usetikzlibrary{arrows.meta, arrows, positioning, shapes, calc, fit, shapes.geometric}
\tikzset{arrow/.style={-Stealth}}

\usepackage{tikz}
\usepackage{newunicodechar}
\newunicodechar{⚙}{$\odot$} % Simple gear symbol replacement

\usepackage{multicol}
\usepackage{placeins} % For \FloatBarrier
\usepackage{svg}
\usepackage{subcaption} % For subfigure support
\usepackage{natbib}
% fontawesome5 removed - using simple symbols instead
% TikZ removed - using high-resolution PDF figures instead
% pgfplots for high-quality training dynamics plots
\usepackage{pgfplots}
\pgfplotsset{compat=newest}

% --- Code Listing Setup (listingsutf8) ---
% This block configures the appearance of code snippets.
% The duplicate block from your original file has been removed.
\usepackage{listingsutf8}
\lstset{
  inputencoding=utf8,
  extendedchars=true,
  literate={├}{{\textbar}}1 {└}{{\textbar}}1 {│}{{\textbar}}1 {─}{{-}}1,
  basicstyle=\ttfamily\footnotesize,
  numbers=left,
  numberstyle=\tiny\color{gray},
  frame=single,
  breaklines=true,
  backgroundcolor=\color{gray!10},
  keywordstyle=\color{blue},
  commentstyle=\color{green!50!black},
  stringstyle=\color{purple!80!black}
}


% ---------- Title & Author ----------
\title{ArGen: Auto-Regulation of Generative AI via GRPO and Policy-as-Code}
\author{
  Kapil Madan \\
  Principled Evolution \\
  \texttt{<EMAIL>}
}
\date{\today}

% ---------- Copyright Notice ----------
% Redefine the notice string to include copyright information
\makeatletter
\renewcommand{\@noticestring}{%
  Preprint. Under review. \\
  \vspace{2pt}
  © 2025 Kapil Madan. All rights reserved. This is a preprint of a work under review for publication.%
}
\makeatother

% ---------- Document Body ----------
\usepackage{float}

% --- Hyperlink Setup (hyperref) ---
% This package adds clickable links and should generally be loaded last.
\usepackage[hidelinks]{hyperref}
\begin{document}

\maketitle

\begin{abstract}
\input{sections/abstract}
\end{abstract}

% --- Main Content ---   
% Ensure each of these files starts with a \section{...} command.
\section{Introduction}
\input{sections/introduction}
\section{Related Work}
\input{sections/related-work}
\section{The ArGen Framework: Architecture for Auto-Regulation}
\input{sections/conceptual-framework}
\section{Implementation of ArGen}
\input{sections/implementation}
\section{Case Study: Aligning a Medical AI Assistant with Dharmic-Inspired Principles}
\input{sections/case-study}
\section{Ethical Considerations and Positionality}
\input{sections/ethical-considerations}
\section{Discussion}
\input{sections/discussion}
\section{Conclusion}
\input{sections/conclusion}

% ---------- Appendices ----------
% Use the standard \appendix command. This switches numbering to A, B, C...
% The 'appendix' package and 'appendices' environment have been removed.
\appendix
\section{Technical Appendix}
\label{appendix:technical-details}
\input{sections/technical-appendix}

% ---------- References ----------
\FloatBarrier % Ensures all figures appear before the references

\bibliographystyle{plainnat}
\bibliography{references}

\end{document}