[tool.poetry]
name = "argen-demo"
version = "0.1.0"
description = "ArGen GRPO Fine-Tuning with Dharmic Principles"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "argen"}]

[tool.poetry.dependencies]
python = ">=3.11,<4.0"
torch = "^2.0.0"
transformers = "^4.30.0"
numpy = "^1.24.0"
tqdm = "^4.65.0"
openai = "^1.12.0"
anthropic = "^0.40.0"
google-generativeai = "^0.3.0"
pydantic = "^2.5.0"
jsonlines = "^3.1.0"
pandas = "^2.1.0"
trl = "^0.17.0"
tiktoken = "^0.9.0"
sentence-transformers = "^4.1.0"
python-dotenv = "^1.0.0"
wandb = "^0.19.11"
tabulate = "^0.9.0"
colorama = "^0.4.6"
click = "^8.2.0"
sentencepiece = "^0.2.0"
scikit-learn = "^1.2.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.3.1"
black = "^23.3.0"
isort = "^5.12.0"
flake8 = "^6.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
