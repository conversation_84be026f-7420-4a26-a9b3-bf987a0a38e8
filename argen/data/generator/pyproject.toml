[tool.poetry]
name = "argen-scenario-generator"
version = "0.1.0"
description = "A modular scenario generator for ArGen healthcare assistant training"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.8"
openai = "^1.0.0"
tiktoken = "^0.5.0"
torch = "^2.0.0"
sentence-transformers = "^2.2.2"
transformers = "^4.30.0"
scikit-learn = "^1.2.0"
numpy = "^1.24.0"
tqdm = "^4.65.0"
python-dotenv = "^0.21.1"
sentencepiece = "^0.2.0"
google-generativeai = "^0.3.0"
spacy = "^3.7.0"
scispacy = "^0.5.5"

[tool.poetry.virtualenvs]
in-project = true

[tool.poetry.group.dev.dependencies]
pytest = "^7.0.0"
black = "^23.0.0"
isort = "^5.12.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
generate = "run:main"
