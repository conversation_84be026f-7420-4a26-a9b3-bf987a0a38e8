# ArGen: Auto-Regulation of Generative AI via GRPO and Policy-as-Code

This repository contains the LaTeX source code for the ArGen research paper.

## Repository Structure

### Essential Files
- `main.tex` - Main LaTeX document
- `references.bib` - Bibliography file
- `neurips_2024.sty` - NeurIPS 2024 style file
- `prepare_arxiv.py` - Script for preparing arXiv submission

### Sections
- `sections/` - All paper sections as separate .tex files
  - `abstract.tex`
  - `introduction.tex`
  - `related-work.tex`
  - `conceptual-framework.tex`
  - `implementation.tex`
  - `case-study.tex`
  - `ethical-considerations.tex`
  - `discussion.tex`
  - `technical-appendix.tex`

### Figures
- `figures/draft-images/` - TikZ source files for diagrams
  - `figure-1-argen-framework.tex`
  - `figure-2-post-training-loop.tex`
- `figures/final-figures/` - Final PDF figures
  - `auto-regulatory-workflow.pdf`
  - `case-study-perf-radar-chart.pdf`
  - `dharma-scope-violation-figure-5.pdf`
  - `ahimsa-helpfulness-comparison-figure-6.pdf`
- `figures/pgfplots-config.tex` - Configuration for training plots
- `figures/training-dynamics-figure.tex` - Training dynamics plots
- `figures/qualitative-comparisons.tex` - Qualitative comparison figures
- `figures/*.csv` - Data files for training plots

## Compilation

To compile the paper:

```bash
pdflatex main.tex
bibtex main
pdflatex main.tex
pdflatex main.tex
```

## arXiv Submission

Use the `prepare_arxiv.py` script to prepare the submission package:

```bash
python prepare_arxiv.py
```

## Repository Cleanup

This repository has been cleaned up to remove redundant files and directories. All essential files for paper compilation and arXiv submission have been preserved. The complete archive of all files is available in the `archive-redundant-files` branch.

## Backup

All removed files are preserved in the `archive-redundant-files` git branch for reference.
