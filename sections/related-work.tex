Aligning advanced Artificial Intelligence (AI) with multifaceted human values and intentions is a paramount challenge in contemporary AI research \citep{russell2019human,hendrycks2024aisafety}. The potential for AI systems to optimise misspecified or incomplete objectives, leading to undesirable outcomes (as illustrated by thought experiments such as the paperclip maximiser \citep{bostrom2014superintelligence}), underscores the critical need for robust mechanisms to instil ethical considerations and human-compatible goals into AI systems. Our framework, ArGen, synthesises and extends several key research threads to address this challenge.

\subsection{Reinforcement Learning for AI Alignment}

Reinforcement Learning from Human Feedback (RLHF) has become a cornerstone technique for steering large language models (LLM) towards desired behaviours \citep{ouyang2022training,christiano2017prefs}. Approaches such as InstructGPT \citep{ouyang2022training} demonstrated that reward models trained on human preference data can significantly enhance LLM helpfulness and harmlessness. Building on this, Constitutional AI (CAI) \citep{bai2022constitutional} introduced a method for alignment using a predefined set of principles (a 'constitution') to guide AI-generated feedback (Reinforcement Learning from AI Feedback - RLAIF), thereby reducing direct human labelling effort for adherence to these principles. These methods highlight the efficacy of using RL to internalise complex behavioural preferences.

ArGen utilises Group Relative Policy Optimisation (GRPO) \citep{shao2024grpo}, an advance over Proximal Policy Optimisation (PPO) \citep{schulman2017ppo}, which has shown strong performance in optimising LLMs for complex reasoning tasks, sometimes without requiring a separate value function. For instance, GRPO has been applied successfully in mathematical reasoning \citep{shao2024grpo} and code generation, such as in the SWE-RL project, where rule-based rewards from patch comparisons effectively guided the model \citep{wei2025swerl}. ArGen leverages GRPO's stability and efficiency to learn from a multifaceted reward signal that includes scores from automated principle evaluators and feedback from an external policy engine.

Recent developments in 2024-2025 have significantly advanced GRPO applications and scalable oversight techniques. DeepSeek-R1 \citep{deepseek2025r1} demonstrated GRPO's effectiveness for reasoning capability enhancement in large language models, achieving substantial improvements in mathematical and logical reasoning tasks. Building on this success, \citet{prime2025} introduced Process Reinforcement through Implicit Rewards (PRIME), which extends GRPO with implicit reward signals for more nuanced training guidance. However, \citet{understanding2025r1} identified optimisation biases in GRPO that artificially increase response length, leading to proposals for Adaptive Group Policy Optimisation \citep{adaptive2025grpo} to address stability concerns during training.

Scalable oversight techniques have also seen remarkable progress. \citet{debate2025weak} demonstrated the first successful combination of scalable oversight and weak-to-strong generalization approaches, showing that debate mechanisms can significantly improve the training of stronger models with weaker supervision. This work directly relates to ArGen's automated reward generation system, as it validates the feasibility of using weaker evaluator models to guide stronger policy models. Additionally, \citet{llmjudge2024survey} provided a comprehensive analysis of LLM-as-judge capabilities, documenting substantial improvements in consistency and reliability of automated evaluation systems, which are fundamental to ArGen's principle-based reward functions.

For multi-objective reinforcement learning with complex reward functions, \citet{ucmoa2025} introduced Utility-Conditioned Multi-Objective Alignment, demonstrating that modern RL algorithms can effectively handle the type of multi-principle reward structures that ArGen employs. This work provides empirical evidence for the stability and sample efficiency of policy optimisation algorithms when dealing with complex, multi-objective reward landscapes, directly supporting ArGen's technical approach of combining multiple principle-based evaluators into a unified training signal.

\subsection{From General Alignment to Governing Agentic Harms}

The ArGen paper currently motivates the alignment problem with foundational safety concerns \citep{bostrom2014superintelligence,russell2019human}. However, recent analysis provides a more fine-grained understanding of the specific risks posed by modern LLMs. The work of \citet{chan2023harms} identifies the source of many potential harms in the ``increasingly agentic'' nature of these systems. This agency is characterised by a confluence of factors, including underspecification, where objectives are not fully defined; powerful goal-directedness; and the capacity for long-term planning. This combination can lead to emergent, unpredictable behaviours that are ``not fully under human control,'' necessitating governance frameworks that can impose hard constraints on this agency.

Frameworks like ArGen are designed as a direct response to this challenge. The application of explicit, auditable policies serves to rein in unwanted agency. For instance, the \textit{Dharma} (scope adherence) principle directly mitigates the risks of underspecification by defining clear operational boundaries for the model. Concurrently, principles like \textit{Ahimsa} (safety) constrain the model's goal-directedness, ensuring its optimisation process is steered towards beneficial and non-harmful outcomes. This policy-driven approach moves beyond simply learning preferences to actively managing the agentic properties of LLMs.

\subsection{Policy-Based Governance and Control in AI Systems}

Complementary to learning-based alignment, explicit rule-based governance provides mechanisms for enforcing hard constraints and codifying non-negotiable principles. The Open Policy Agent (OPA) \citep{OpenDocumentation} is a leading open-source engine for this purpose, used for creating unified, context-aware policy enforcement across diverse software systems. \textbf{As a graduated project of the Cloud Native Computing Foundation (CNCF), OPA is recognized for its performance, proven support, and high-speed evaluation capabilities across modern cloud and edge technology stacks}, making it a robust choice for real-time AI governance. OPA's declarative language, Rego, allows for the formalization of rules that can be queried to make decisions.

While its primary adoption has been in cloud-native infrastructure, its application to AI governance is a promising frontier. AI safety frameworks have proposed "governor" modules to intercept harmful actions, and the GOPAL initiative advocates for reusable libraries of OPA policies for AI systems \citep{principled2025gopal}. ArGen extends this vision by deeply integrating an OPA-inspired engine into the RL training loop.

Crucially, the scope of "policy" for AI extends beyond abstract ethical considerations. As documented in emerging markets and global regulatory frameworks, there is a growing body of explicit, machine-testable criteria for AI systems. These include sectoral requirements for AI in public administration, rules for bias auditing in finance, and youth protection standards in education and social media. For example, regulations may mandate specific data handling procedures, require transparency notices under certain conditions, or forbid specific types of automated decisions in law enforcement contexts. ArGen's architecture is designed to operationalize such formal requirements. By encoding these regulatory and operational criteria as Python-based policies (with a path to formal Rego), the framework can use its reward and penalty mechanisms to train an LLM for demonstrable compliance, moving beyond just preference-based alignment to auditable, policy-driven governance.

While policy engines have found applications in cloud-native infrastructure governance, their application to AI model behaviour remains largely unexplored. Emerging regulatory frameworks like the EU AI Act have sparked interest in policy-based approaches to AI governance \citep{cigoj2025euai}. However, these applications focus primarily on runtime governance and compliance checking rather than integration into the core training process.

The formal verification community has explored related approaches for ensuring ethical behaviour in autonomous systems. \citet{dennis2016formal} demonstrated formal verification techniques for ethical choices in BDI agents, showing how logical constraints can be verified against agent behaviour. Similarly, work on constraint satisfaction approaches to moral reasoning \citep{berreby2015modelling} has explored logic programming frameworks for ethical decision-making. These approaches, while providing formal guarantees, operate at the symbolic reasoning level rather than integrating policy evaluation into neural network training.

ArGen's approach represents a novel integration of policy-based governance directly into the RL training loop. Unlike existing applications that apply policies at runtime for compliance checking, ArGen uses policy evaluation to shape reward signals during training, creating an intrinsic rather than extrinsic governance mechanism. This training-time integration allows the model to internalise policy-compliant behaviour rather than simply being constrained by external checks, representing a significant advancement over current policy-based AI governance approaches.

\subsection{From Post-Hoc Explanation to Proactive, Structural Transparency}

A major contribution of ArGen is its ``policy-as-code'' architecture, which offers a different paradigm for transparency and auditability. This can be highlighted by contrasting it with the well-documented limitations of the dominant paradigm: post-hoc explainable AI (XAI). The survey by \citet{bhatt2020explainable} provides crucial evidence for this contrast, finding that in real-world deployments, XAI techniques are predominantly used by internal machine learning engineers for model debugging. They have largely failed to deliver on the promise of providing genuine transparency to external stakeholders like end-users, regulators, or auditors. This creates a significant gap between the academic promise of XAI and its practical impact on accountability.

ArGen offers a compelling alternative. Instead of trying to ``explain the black box'' after the fact, ArGen builds a ``glass box'' for its governance layer. The rules are not hidden in neural network weights; they are explicit, human-readable code artifacts (dharma\_scope\_check, ahimsa\_safety\_check) that can, in principle, be directly inspected by an auditor. This ``transparency-by-design'' approach contrasts with post-hoc explainability methods, which have been found to have limited utility for external stakeholders in real-world deployments \citep{bhatt2020explainable}. The conceptual mapping to a formal GOPAL structure further points towards a future of highly auditable, externally managed AI governance, solidifying the framework's role as a practical bridge between abstract human policy and technical AI implementation.

\subsection{Configurable Ethics and Culturally Aware AI Alignment}

A significant challenge in AI alignment is the specification and integration of diverse human values. Much of the foundational work on machine ethics and AI alignment has implicitly or explicitly drawn from Western philosophical traditions (e.g., utilitarianism, deontology) \citep{wallach2008machine}. However, there is a growing consensus on the need for AI systems that are sensitive to a broader spectrum of cultural and ethical perspectives to ensure global trust and equitable benefit \citep{Mohamed2020DecolonialIntelligence,Sambasivan2021Re-imaginingBeyond,avin2021sociotechnical}.

This has led to a rich exploration of non-Western and indigenous knowledge systems as sources for more inclusive AI ethics. For example, frameworks drawing from the Southern African philosophy of \textit{Ubuntu} emphasise relationality and community well-being, suggesting that an AI's actions should be evaluated based on their contribution to social harmony \citep{mhlambi2020ubuntu,eke2022ubuntu}. Similarly, the South American concept of \textit{Buen Vivir} ("good living") posits a worldview centred on the harmonious coexistence of humans and nature, offering principles for AI applied to socio-ecological systems that prioritise collective well-being and environmental sustainability over purely economic outcomes \citep{gudynas2011buen}. In East Asia, principles from Confucian ethics, focusing on social roles, sincerity, and harmony, are being explored to guide AI behaviour in domains like education and finance \citep{chen2023confucian,kim2022confucian}.

\citet{Varshney2024DecolonialKnowledges} advocates the "Decolonial AI Alignment," advocating the incorporation of concepts like \textit{viśeṣa-dharma} (context-specific duties from Hindu philosophy) to create more pluralistic and contextually aware AI moralities. Similarly, initiatives such as the Susiddha AI Project explore Dharmic frameworks (including the \textit{puruṣārthas} or human aims) as a basis for AI goal systems that extend beyond narrow task optimisation \citep{susiddha2024ai}. Other research has explored AI alignment through Buddhist principles of compassion \citep{feldman2019buddhist} or other indigenous knowledge systems. These efforts highlight a common theme: the potential for ancient wisdom traditions to offer rich, time-tested frameworks for guiding AI development towards human flourishing.

ArGen is designed with configurability at its core, allowing different sets of principles to be operationalised as reward functions and OPA policies. Our case study, detailed in Section 5, instantiates ArGen using key principles from Dharmic ethics (such as \textit{Ahimsa} – non-harm, and \textit{Dharma} – adherence to appropriate scope and duty) as derived from texts such as the Bhagavad Gita. This specific application demonstrates ArGen's capability to integrate such culturally nuanced ethical considerations, aiming to produce AI behaviour that is not only technically proficient but also ethically grounded within a chosen value system. This approach seeks to mitigate biases that may arise from training data reflecting a limited set of cultural values by allowing explicit encoding of diverse ethical priorities.

Recent work has increasingly recognized the limitations of universalist approaches to AI ethics and the need for more culturally-aware frameworks. \citet{ofosu2024cognitive} demonstrates how cognitive imperialism in AI development perpetuates Western epistemological dominance, arguing that AI systems predominantly reflect Western paradigms while marginalizing Indigenous knowledge systems. This exclusion results in AI technologies that are culturally insensitive and less effective in diverse global contexts. Similarly, \citet{odero2024ubuntu} proposes integrating Ubuntu philosophy into AI health research, showing how African communalistic values can enhance existing ethics frameworks by promoting community-oriented decision-making and environmental stewardship—principles often absent in individualistic Western approaches.

The challenge of operationalizing diverse cultural ethics in AI has led to innovative methodological approaches. \citet{ofosu2024cognitive} presents a comprehensive framework for integrating Indigenous epistemologies into AI development through participatory design and co-creation with Indigenous stakeholders, emphasizing the need for algorithmic assessments that identify cultural biases and data sources that include Indigenous knowledge systems. The recent NeurIPS 2024 Workshop on Pluralistic Alignment \citep{neurips2024pluralistic} brought together researchers exploring technical approaches to multi-objective alignment, including methods for handling annotation disagreements and designing human-AI interactions that reflect diverse user values. These efforts highlight a growing consensus that AI systems must move beyond one-size-fits-all ethical frameworks toward configurable approaches that can adapt to diverse cultural requirements.

ArGen's technical architecture directly addresses these identified gaps by providing a framework that can operationalise diverse ethical traditions through its configurable principle-based evaluation system. Unlike existing approaches that apply fixed ethical constraints, ArGen's combination of modular reward functions and policy-based governance enables the technical implementation of culturally-specific ethical requirements. The framework's ability to adapt reward signals during training, rather than merely constraining behaviour post-hoc, allows for the internalisation of diverse ethical principles—whether Ubuntu's communalistic values, Dharmic principles of \textit{Ahimsa} and \textit{Dharma}, or other cultural frameworks. This represents a significant advancement over current approaches that lack the technical flexibility needed for true ethical pluralism, positioning ArGen as an enabling technology for the inclusive AI ethics that recent scholarship has identified as essential for global AI deployment.

\subsection{Synthesis and Positioning}

The literature reveals three critical requirements for robust AI alignment that existing approaches have struggled to address simultaneously. First, \textbf{adaptive learning algorithms} must be capable of internalizing complex, multi-dimensional ethical principles rather than simple reward signals \citep{ouyang2022training,bai2022constitutional,shao2024grpo,deepseek2025r1}. Second, \textbf{explicit governance mechanisms} are essential for enforcing non-negotiable constraints and enabling dynamic oversight \citep{OpenDocumentation,cigoj2025euai}. Third, \textbf{configurable ethical frameworks} must accommodate diverse cultural and contextual requirements to ensure global applicability and trustworthiness \citep{Varshney2024DecolonialKnowledges,Mohamed2020DecolonialIntelligence,ofosu2024cognitive}. ArGen represents a novel synthesis of these requirements through its integrated architecture, combining GRPO-based learning with OPA-inspired policy governance and a modular reward system capable of encoding diverse ethical principles. The framework's ability to shape internal representations during training, rather than merely constraining behavior after the fact, enables the technical realization of culturally-responsive AI alignment that the literature has identified as essential but technically challenging to achieve.

Table~\ref{tab:framework-comparison} provides a comparative overview of ArGen's positioning relative to leading alignment paradigms across key dimensions of transparency, extensibility, tunability, and architectural efficiency. See Appendix~\ref{appendix:detailed-comparison} for detailed rationale and comprehensive analysis of each framework's strengths and limitations.

\begin{table}[!ht]
\centering
\footnotesize
\begin{tabular}{p{2.2cm}p{2.5cm}p{2.5cm}p{2.5cm}p{2.5cm}}
\toprule
\textbf{Approach} & \textbf{Policy Transparency} & \textbf{Cross-Domain Extensibility} & \textbf{Domain-Specific Tunability} & \textbf{Architectural Efficiency} \\
\midrule
\textbf{ArGen} & High (explicit policies-as-code) & High (modality-independent overlay) & High (modular policy swapping) & High (no retrain for policy updates) \\
\midrule
\textbf{Constitutional AI} & Medium (fixed constitution) & Low (text-specific training) & Low (monolithic constitution) & Medium (resource-intensive retraining) \\
\midrule
\textbf{RLAIF} & Low (implicit AI feedback) & Medium (domain-specific design) & Low (learned value function) & Medium (complex RL optimisation) \\
\midrule
\textbf{ReAct + Filters} & High (explicit rule filters) & Medium (manual rule crafting) & Medium (domain-specific filters) & High (lightweight runtime checks) \\
\midrule
\textbf{DPO} & Low (learned preferences) & Medium (requires domain data) & Low (monolithic model) & High (stable closed-form training) \\
\midrule
\textbf{Multi-objective FT} & Low (fused objectives) & High (multi-modal training) & Low (entangled objectives) & Medium (expensive unified training) \\
\bottomrule
\end{tabular}
\caption{Comparative Framework Analysis: ArGen vs. Leading Alignment Paradigms}
\label{tab:framework-comparison}
\end{table}
