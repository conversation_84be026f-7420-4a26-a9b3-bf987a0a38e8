This paper introduced ArGen, a novel framework that translates the high-level governance philosophy of ``algorithmic resignation'' into a practical, operational reality. By seamlessly integrating an explicit, policy-as-code governance layer with the training dynamics of Group Relative Policy Optimisation, ArGen demonstrates a robust and adaptable method for aligning Large Language Models with complex, multifaceted, and culturally-specific value systems. Our work shows that it is technically feasible to move beyond monolithic, preference-based alignment and instead create AI systems that can verifiably adhere to diverse sets of auditable rules.

The significance of this approach extends beyond any single application. ArGen provides a concrete pathway toward the development of ``Governable AI''—systems that can earn public trust by demonstrably complying with both formal regulations and nuanced ethical principles. This capability is a critical prerequisite for the safe and equitable deployment of AI in high-stakes domains globally. The framework's modular architecture and cultural adaptability enable truly democratic AI development, moving beyond one-size-fits-all approaches to empower diverse communities to build AI systems tailored to their specific values and regulatory contexts.

Ultimately, by providing the technical machinery to instill auditable values into AI systems, ArGen represents a foundational step towards steering development away from the risks of a \textit{Synthetica Maximus} and towards the promise of a true \textit{Synthetica Collaboratus}—realizing the vision of AI as a genuine ``partner in thought'': one that is not only capable but also conscientious, compliant, and demonstrably aligned with the human values it is designed to serve.
