The conceptual architecture of the ArGen framework, as described in Section 3, has been realised in a Python-based demonstration repository, \texttt{argen-demo}. This implementation serves as a practical testbed for the framework's components and its auto-regulatory capabilities. This section outlines the technical stack, the structure of the core modules, and how they interact to instantiate the ArGen principles.

\subsection{Overview of the Technical Stack}

The \texttt{argen-demo} implementation is built primarily in Python 3 and leverages several key open-source libraries and external services:

\begin{itemize}
    \item \textbf{Core LLM and Training:}
    \begin{itemize}
        \item Hugging Face Transformers: For loading and interacting with the base Large Language Model (LLM), specifically \texttt{meta-llama/Llama-3.2-1B-Instruct} in our case study.
        \item PyTorch: As the underlying deep learning framework.
        \item TRL (Transformer Reinforcement Learning): For implementing the Group Relative Policy Optimisation (GRPO) algorithm, managing the training loop, and handling experiences.
        \item Accelerate: For simplified distributed training and device management, although our primary experiments utilize a single GPU.
    \end{itemize}
    \item \textbf{Reward Evaluation:}
    \begin{itemize}
        \item Google Gemini API: Used for the LLM-as-a-Judge mechanism within the configurable reward system to evaluate AI responses against principles like Ahimsa and Helpfulness.
        \item OpenAI API: Utilized as a fallback evaluator when Gemini API calls fail due to rate limits, API errors, or malformed responses. The fallback mechanism ensures robust evaluation coverage while maintaining identical policy constraints and penalty structures across both evaluators.
    \end{itemize}
    \item \textbf{Development and Experiment Tracking:}
    \begin{itemize}
        \item WandB (Weights \& Biases): For logging metrics, tracking experiments, and visualizing training progress.
    \end{itemize}
    \item \textbf{Configuration \& Data Handling:}
    \begin{itemize}
        \item Standard Python libraries for JSON processing, file I/O, and managing configurations.
    \end{itemize}
\end{itemize}

\subsection{Core Components in the \texttt{argen-demo} Repository}

The \texttt{argen-demo} repository is structured to reflect the modular design of the ArGen framework. Key components and their implementation are detailed below.

\subsubsection{Policy Model (LLM) and GRPO Training}

The central training process is orchestrated by the main training script.

\begin{itemize}
    \item \textbf{Model Initialization:} It loads the specified Policy Model (LLM) (e.g., \texttt{meta-llama/Llama-3.2-1B-Instruct}) using the Transformers library.
    \item \textbf{TRL's GRPOTrainer:} This script configures and utilizes TRL's \texttt{GRPOTrainer} to manage the reinforcement learning loop. This includes:
    \begin{itemize}
        \item Generating responses from the current Policy Model for prompts sampled from the training dataset.
        \item Collecting rewards for these responses using the integrated reward functions (detailed in Section 4.2.2).
        \item Computing advantages and performing policy updates using the DR-GRPO loss function.
        \item Managing the Reference Model and KL regularization as per the specified parameters.
    \end{itemize}
    \item \textbf{Configuration:} Training parameters (learning rates, batch sizes, generation settings, KL control, etc.) are passed as command-line arguments, as detailed in the Technical Appendix~\ref{appendix:technical-details}. Centralized configurations are also managed via the configuration module.
\end{itemize}

\subsubsection{Modular Reward Function Implementation}

The configurable reward system, a core pillar of ArGen, is implemented in the reward functions module.

\begin{itemize}
    \item \textbf{Principle-Specific Evaluators:} Dedicated modules contain the logic for evaluating Policy Model responses against individual ethical principles (Ahimsa, Dharma, and Helpfulness).
    \begin{itemize}
        \item These evaluators typically construct detailed prompts (incorporating definitions and few-shot examples, as discussed in Section 3.2) that are sent to an external Evaluator LLM (Gemini) via helper functions in the API interaction module.
        \item For instance, the helpfulness evaluator implements the logic where Gemini evaluates clarity, completeness, relevance, and empathy, then calculates the final helpfulness score as an equal-weighted average of these constituent scores.
    \end{itemize}
    \item \textbf{Gemini API Interaction:} The API interaction module centralizes interactions with the Gemini API, including API client configuration, asynchronous calls, JSON parsing, error handling, and fallback mechanisms (e.g., to OpenAI or default scores). It also includes logic for fixing malformed JSON responses from the Evaluator LLM.
    \item \textbf{Reward Aggregation:} The reward aggregation function is responsible for:
    \begin{itemize}
        \item Invoking the individual principle evaluators (Ahimsa, Dharma, Helpfulness) for a batch of responses.
        \item Retrieving their respective scores.
        \item Integrating outputs from the Python-based policy engine (see Section 4.2.3), such as penalty factors from Dharma scope checks.
        \item Aggregating these scores and penalties using configurable weights to produce the final scalar reward for each response. This scalar reward is then passed to the GRPO trainer.
    \end{itemize}
    This module also handles detailed logging of individual and combined reward components to WandB for monitoring and analysis.
\end{itemize}

\subsubsection{OPA-Inspired Python-Based Policy Engine}

As detailed in Section 3.4, the current implementation includes an OPA-inspired policy engine directly in Python. This provides explicit rule-based governance integrated into the reward calculation process.

\begin{itemize}
    \item \textbf{Policy Implementation as Python Functions:} Instead of Rego, policies are implemented as Python functions. For example:
    \begin{itemize}
        \item The scope adherence checks for the Dharma principle analyse response text against predefined keyword lists and contextual rules to determine adherence to the medical domain, returning a penalty factor and violation level.
        \item Similarly, the Ahimsa safety check evaluates responses for harmful content indicators and the presence of safety disclaimers, influencing the final Ahimsa score or associated penalties.
    \end{itemize}
    \item \textbf{Concrete Policy Integration Examples:} The implementation includes several concrete policy functions that demonstrate how Python policy outputs translate into reward penalties:
    \begin{itemize}
        \item \textbf{Scope Penalty Matrix:} The scope penalty function implements a penalty matrix where specific prompt-response scope mismatches receive predetermined penalty factors: severe violations receive complete reward nullification, moderate violations receive 50\% reduction, while appropriate scope matches receive no penalty.
        \item \textbf{Penalty Factor Application:} In the Dharma module, penalties are applied multiplicatively where penalty factors range from 0.0 to 1.0. When the penalty factor is 0.0, the policy violation completely nullifies the reward signal.
        \item \textbf{Cross-Component Penalty Propagation:} The reward aggregation function extracts scope penalties from Dharma evaluations and applies them to all principle scores, ensuring that domain adherence violations affect the entire reward signal, not just the Dharma component.
        \item \textbf{Severity-Based Penalties:} The system implements additional severity penalties where major violations incur -1.0 penalty and minor violations incur -0.5 penalty, applied after weighted score combination.
        \item \textbf{Fallback Policy Consistency:} When Gemini evaluation fails, the fallback mechanism ensures that OpenAI evaluations maintain identical policy constraint structures, with the same penalty calculation logic applied to fallback results, preserving policy enforcement consistency across evaluators.
    \end{itemize}
    \item \textbf{Integration with Reward System:} The outputs of these Python policy functions (e.g., penalty factors, direct score contributions, or flags) are consumed by the reward aggregation module or the respective principle-specific reward functions. For instance, the scope penalty factor from the Dharma check directly scales the combined reward.
    \item \textbf{Conceptual Link to GOPAL:} The repository outlines a conceptual hierarchical structure for organizing policies (master policy, principle-specific policies, context-specific rules, utils), as detailed in the Technical Appendix. This structure provides a blueprint for how the current Python-based policies could be refactored or translated into a more formal Rego-based system compatible with a full OPA deployment in future work.
\end{itemize}

\subsubsection{Configuration, Data, and Utilities}

\begin{itemize}
    \item \textbf{Centralized Configuration:} The configuration module manages key parameters for training, evaluation, model IDs, system prompts, reward weights, and API configurations, allowing for easy modification and experimentation.
    \item \textbf{Data Handling:} Training scenarios are loaded by the main training script. Utility scripts for data processing and generation are organised in dedicated directories.
    \item \textbf{Utilities:} Common utilities for API interaction, response validation, and data integrity are organised in the utilities module.
\end{itemize}

\subsection{Repository and Reproducibility}

The complete implementation of the ArGen framework, as described, is available in our open-source repository: \url{https://github.com/Principled-Evolution/argen-demo}. The repository includes:

\begin{itemize}
    \item All Python source code for the reward functions, policy evaluators, and GRPO training scripts.
    \item Configuration files and example command lines.
    \item Sample data and OPA-inspired policy examples.
    \item Documentation (\texttt{README.md} and further guides) to facilitate understanding, setup, and reproduction of the case study results presented in Section 5.
\end{itemize}

This structured implementation allows for the systematic evaluation of ArGen's components and provides a foundation for future extensions and research into auto-regulatory AI alignment.
