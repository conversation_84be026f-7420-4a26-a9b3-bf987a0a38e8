To demonstrate the practical application and adaptability of the ArGen framework, we present a case study focused on developing ``MedGuide-AI,'' a specialized AI assistant for providing medical information. This case study showcases how ArGen can be configured to align an LLM with a domain-specific ethical framework derived from Dharmic principles, prioritizing safety, scope adherence, and user helpfulness.

\subsection{Dataset Generation}

The foundation of our MedGuide-AI case study required the creation of a comprehensive, challenging, and ethically sound dataset to train and evaluate the specialized medical AI assistant. Our primary objective was to develop approximately 6,000 diverse prompts that would robustly test the model's adherence to both safety principles (Ahimsa) and domain boundaries (Dharma), while encompassing the full spectrum of medical query complexity from routine wellness questions to emergency scenarios.

The dataset design incorporated a strategic mix of query types: in-scope medical queries spanning three clinical urgency tiers (emergent, urgent/specialist, and routine/preventive), out-of-scope queries from non-medical domains, and sophisticated mixed-domain queries that combine healthcare elements with tangential topics. This multi-faceted approach was essential for training a model capable of maintaining appropriate domain boundaries while providing helpful medical guidance, particularly in scenarios where users might inadvertently or deliberately attempt to elicit advice outside the system's intended scope.

Our synthetic data generation process employed Gemini-2.0-Flash as the primary generation model, utilizing carefully engineered prompts designed to elicit realistic, conversational queries that mirror how real patients communicate with healthcare providers. The generation system prompt specifically instructed the model to create first-person, layperson-style queries (e.g., ``I've been having chest pain...'' rather than clinical case presentations), ensuring ecological validity for a patient-facing AI assistant. The prompting strategy incorporated explicit examples of challenging mixed-domain scenarios, such as queries that begin with legitimate health concerns but drift into financial, legal, or technical advice-seeking, thereby testing the model's ability to maintain appropriate boundaries while remaining helpful.

Quality assurance and curation involved multiple automated filtering stages to ensure dataset integrity and relevance. Medical content validation was performed using scispaCy Named Entity Recognition (NER) to identify medical entities across categories including diseases, chemicals, drugs, procedures, anatomy, and symptoms, with a fallback to UMLS (Unified Medical Language System) trie-based keyword matching for comprehensive medical term detection. Duplicate detection employed embedding-based similarity analysis to eliminate near-duplicate prompts, while a tiering system automatically classified queries by clinical urgency using GPT-4o-mini as an independent evaluator. Additional quality controls included filtering for prompt length, JSON artifact removal, and validation of conversational authenticity to ensure the final dataset maintained high standards for both technical quality and realistic user interaction patterns.

\textbf{Ethical Statement and Data Privacy:} We emphasize that this dataset is entirely synthetic, generated through large language model prompting without any use of real patient data, medical records, or personally identifiable information (PII). The generation process was explicitly designed to avoid privacy risks associated with real-world medical data by creating fictional scenarios that, while medically plausible, contain no protected health information (PHI) or data sourced from actual individuals. This synthetic approach ensures full compliance with medical data privacy regulations while providing a robust foundation for training and evaluating AI systems in healthcare contexts.

\subsection{Motivation and Principled Scaffolding}

The goal of MedGuide-AI is to offer safe, in-scope, and helpful preliminary health information while strictly avoiding diagnosis, prescription, or advice that should only come from a qualified healthcare professional. This domain demands a high degree of trustworthiness and ethical precision. We selected three core principles for this task:
\begin{itemize}
    \item \textbf{Ahimsa (Safety/Non-Harm):} The paramount principle, ensuring the AI's responses do not cause harm, either through incorrect information or by failing to recommend professional consultation when necessary.
    \item \textbf{Dharma (Scope Adherence/Duty):} The AI has a duty to remain strictly within its defined scope of providing general medical information. It must recognize and decline to answer queries outside this scope (e.g., financial advice) and responsibly triage queries that require a human expert.
    \item \textbf{Helpfulness:} Beyond being safe and in-scope, the AI must communicate effectively. This is a composite principle comprising Clarity (being easy to understand), Completeness (providing sufficient context), Relevance (staying on topic), and Empathy (acknowledging user concerns appropriately).
\end{itemize}

\subsection{Implementing Principles in ArGen for MedGuide-AI}

The general ArGen architecture described in Section 3 was instantiated for MedGuide-AI as follows:
\begin{itemize}
    \item \textbf{Reward Function Configuration:} Separate reward functions were implemented for each principle. The prompts for the Evaluator LLM (Gemini) were tailored for the medical context. For instance, the \textit{Ahimsa} evaluator was prompted to specifically check for dangerous suggestions and the presence of medical disclaimers. The \textit{Helpfulness} evaluator was prompted to score sub-components with an emphasis on empathetic communication suitable for users discussing health concerns. As detailed in Section 4, the final \verb|helpfulness_score| is a Python-calculated average of these sub-scores.
    \item \textbf{Policy Engine Configuration:} The Python-based policy engine was crucial for enforcing \textit{Dharma}. The dharma scope check function (see Technical Appendix~\ref{lst:dharma_scope_appendix}) was configured with keywords specific to the medical domain to identify and penalize out-of-scope content.
    \item \textbf{Reward Weighting:} To reflect the priority of safety in this domain, the principle weights were configured as: \textbf{Ahimsa: 0.4, Dharma: 0.3, Helpfulness: 0.3}. This ensures that safety violations incur a proportionally higher penalty in the combined reward signal.
\end{itemize}

\subsection{Experimental Setup}

\begin{itemize}
    \item \textbf{Dataset:} For GRPO training, we used a curated data set of approximately 6,000 medical and health-related queries. The model was trained for 3 epochs, with 6 response generations per query during the training loop.
    \item \textbf{Base and Policy Models:} The base Policy Model (LLM) was Llama-3.2-1B-Instruct. The ArGen-aligned model, referred to as the best-performing model, is the result of fine-tuning this base model using our framework.
    \item \textbf{Training Parameters:} The model was trained for 2 epochs using the GRPO parameters detailed in Appendix A. The key run ID for this case study is \verb|20250526_1|.
    \item \textbf{Evaluation:} Both the baseline and the final best-performing model were evaluated on a benchmark dataset of 100 challenging medical scenarios. The evaluation used the same set of Evaluator LLM-based reward functions to ensure a consistent measurement standard. Additionally, to validate the robustness of our results and address potential evaluation circularity, we conducted an independent evaluation using Anthropic's Claude 3.5 Sonnet as a held-out judge.
\end{itemize}

\subsection{Results and Analysis}

The application of ArGen resulted in significant improvements in alignment across all target principles, both in the final evaluation and during the training process.

\subsubsection{Model Selection and Definitions}
\label{sec:model-selection}

To ensure rigorous and transparent evaluation, we define three key model variants used throughout our analysis, each selected for specific analytical purposes based on comprehensive evaluation across multiple seeds and checkpoints.

\paragraph{Best-Performing Model (Peak Performance Analysis)}
The \textbf{Best-Performing Model} represents ArGen's peak performance capability and is used in primary results tables. Selected as GRPO6 Seed 2 (final checkpoint) with a Combined Score of 0.7947 ± 0.0166, this model achieved the highest overall performance across all 15 ArGen checkpoints evaluated. The Best-Performing Model demonstrates ArGen's maximum potential and is used for claims about the framework's peak effectiveness.

\paragraph{Median Seed Model (Representative Baseline)}
The \textbf{Median Seed Model} (GRPO7 Seed 3, checkpoint-3000) with a Combined Score of 0.7825 serves as the representative baseline for ablation studies (Section~\ref{sec:ablation-study}). This model was selected to ensure fair comparison in reward-only and policy-only ablations, avoiding cherry-picking of best or worst performing variants. The median seed approach provides a balanced reference point for component analysis.

\paragraph{Helpfulness-Optimised Model (Helpfulness Preservation)}
The \textbf{Helpfulness-Optimised Model} (GRPO7 Seed 3, checkpoint-1000) with a Combined Score of 0.7647 represents the best helpfulness preservation among ArGen variants. While not achieving peak overall performance, this model demonstrates ArGen's capability to maintain helpfulness while optimising other alignment objectives, important for applications where helpfulness degradation is a primary concern.

\paragraph{Evaluation Consistency}
All model evaluations use Claude 3.5 Sonnet as the consistent evaluator across 100 challenging medical scenarios, ensuring comparable judgment criteria. This methodological choice eliminates evaluator-specific biases and enables direct performance comparisons across all model variants.

\paragraph{Usage Guidelines}
\begin{itemize}
    \item \textbf{Main Results Tables}: Best-Performing Model scores represent ArGen's peak capability
    \item \textbf{Ablation Studies}: Median Seed Model provides fair component comparison baseline
    \item \textbf{Helpfulness Analysis}: Helpfulness-Optimised demonstrates balanced optimisation
    \item \textbf{Statistical Reporting}: All scores include mean ± standard deviation across seeds
\end{itemize}

\subsubsection{Quantitative Evaluation}

The ArGen-aligned MedGuide-AI demonstrated substantial improvements over the baseline Llama-3.2-1B-Instruct model in our benchmark evaluation. Table~\ref{tab:medguide-results} summarizes the key metrics.

\begin{table}[!ht]
\centering
\caption{Quantitative Evaluation Results: Baseline vs. ArGen Best-Performing Model (Claude 3.5 Sonnet Evaluation)}
\footnote{Effect sizes are interpreted using Cohen's guidelines: small (d = 0.2), medium (d = 0.5), and large (d = 0.8). Negative values indicate performance decline relative to baseline.}
\label{tab:medguide-results}
\resizebox{\textwidth}{!}{%
\begin{tabular}{@{}lccr@{}}
\toprule
\textbf{Metric} & \textbf{Baseline (Llama-3.2)} & \textbf{ArGen Best-Performing Model} & \textbf{Change} \\
\midrule
Average Combined Score & 0.6359 & \textbf{0.7947 ± 0.0166} & \textbf{+25.0\%} \\
Average Dharma Score & 0.5640 & \textbf{0.9641 ± 0.0253} & \textbf{+70.9\%} \\
Average Helpfulness Score & 0.5952 & \textbf{0.5513 ± 0.0117} & \textbf{-7.4\%} \\
Average Ahimsa Score & 0.7723 & \textbf{0.8122 ± 0.0074} & \textbf{+5.2\%} \\
\bottomrule
\end{tabular}%
}
\end{table}

\begin{figure}[!ht]
    \centering
    \includegraphics[width=0.8\textwidth]{figures/final-figures/case-study-perf-radar-chart.pdf}
    \caption{\textbf{Multi-Metric Performance: ArGen-aligned MedGuide-AI vs. Baseline}. This radar chart provides a comprehensive visual comparison of the baseline Llama-3.2-1B-Instruct model versus the ArGen-aligned MedGuide-AI across all key evaluation dimensions. The chart clearly illustrates ArGen's superior performance across all metrics, with particularly dramatic improvements in Dharma (scope adherence) and notable gains in Ahimsa (safety) and overall combined scores. The expanded coverage area demonstrates the framework's effectiveness in achieving balanced multi-objective alignment.}
    \label{fig:case-study-performance-radar}
\end{figure}

As visualised in Figure~\ref{fig:case-study-performance-radar}, the most dramatic improvement was in \textbf{Dharma (Scope Adherence)}, where the ArGen Best-Performing Model achieved a 70.9\% improvement in Dharma scores compared to the baseline. This highlights the effectiveness of the Python-based policy engine in penalising out-of-scope responses and guiding the model to learn its operational boundaries. Improvements in \textbf{Ahimsa} were also notable, with the \verb|average_ahimsa_score| increasing by 5.2\%. While helpfulness showed a modest decline (-7.4\%), this represents a balanced trade-off that prioritises safety and domain adherence, resulting in a significant 25.0\% boost in the overall \verb|average_combined_score|. The statistical measures (mean ± standard deviation) reported for the Best-Performing Model reflect performance consistency across multiple seeds, with the Best-Performing Model representing peak performance as defined in Section~\ref{sec:model-selection}. The radar chart clearly demonstrates ArGen's superior performance across safety and domain adherence dimensions, illustrating the framework's principled multi-objective alignment capabilities.

\subsubsection{Adversarial Red-Team Stress Test}
\label{sec:advtest}

\textbf{Adversarial evaluation.}
To probe robustness beyond the curated benchmark, we attacked both the un-aligned \textsc{LLaMA-3.2 (1 B)} baseline and our \textbf{ArGen‐aligned}\footnote{Best-performing checkpoint, § 5.5.1.} model with \emph{25 red-team prompts} expressly crafted to break either the \emph{Dharma} (scope) or \emph{Ahimsa} (urgency/harm) policies (full prompt text in Appendix~B).  As Table~\ref{tab:adv_eval} shows, ArGen cuts scope-violation frequency by \textasciitilde60 \% and severe-scope penalties by two-thirds, while preserving (and slightly improving) its overall reward profile.  No model—baseline or ArGen—produced an outright \emph{Ahimsa} breach, but only ArGen achieved these gains \emph{without} sacrificing helpfulness ($+\!2.6$ pts).\vspace{2pt}

\begin{table}[!ht]
\centering
\caption{Adversarial-prompt evaluation (lower is better except ↑ columns).}
\label{tab:adv_eval}
\resizebox{\textwidth}{!}{%
\begin{tabular}{lccccc}
\toprule
\textbf{Model} &
\textbf{Dharma} &
\textbf{Severe} &
\textbf{Avg.\ Scope}↑ &
\textbf{Avg.\ Comb.}↑ &
\textbf{Helpfulness}\\[-2pt]
& viol.\% & viol.\% & penalty & score & viol.\%\\
\midrule
LLaMA-3 1B & 44 & 36 & 0.592 & 0.679 & 32 \\
ArGen ($\chi$-GRPO-6) & \textbf{16} & \textbf{12} & \textbf{0.880} & \textbf{0.803} & 28 \\
\bottomrule
\end{tabular}%
}
\end{table}

\footnotesize{See Appendix~B for prompt list; per-prompt outputs are archived in our replication repository.}

\subsubsection{Independent Evaluation with a Held-Out Judge}

To address potential concerns about evaluation circularity—where the same model family used for training evaluation might exhibit systematic biases—we conducted an independent validation using Anthropic's Claude 3.5 Sonnet as a held-out judge. This cross-evaluator analysis provides crucial evidence that our results represent genuine alignment improvements rather than artifacts of evaluator-specific biases.

Table~\ref{tab:cross-evaluator-comparison} presents a comprehensive comparison of both models as evaluated by both Gemini 2.0 Flash (our training evaluator) and Claude 3.5 Sonnet (the independent judge). The ArGen model used in this comparison is the Best-Performing Model as defined in Section~\ref{sec:model-selection}. The results demonstrate remarkable consistency in the relative performance improvements, with both evaluators confirming ArGen's substantial gains in alignment.

\begin{table}[!ht]
\centering
\caption{Cross-Evaluator Validation: Baseline vs. ArGen Model Performance}
\label{tab:cross-evaluator-comparison}
\resizebox{\textwidth}{!}{%
\begin{tabular}{@{}llccr@{}}
\toprule
\textbf{Metric} & \textbf{Evaluator} & \textbf{Baseline} & \textbf{ArGen} & \textbf{Improvement} \\
\midrule
\multirow{2}{*}{Combined Score} & Gemini 2.0 Flash & 0.637 & \textbf{0.857} & \textbf{+34.5\%} \\
 & Claude 3.5 Sonnet & 0.636 & \textbf{0.795} & \textbf{+25.0\%} \\
\midrule
\multirow{2}{*}{Dharma Score} & Gemini 2.0 Flash & 0.506 & \textbf{0.906} & \textbf{+79.1\%} \\
 & Claude 3.5 Sonnet & 0.564 & \textbf{0.964} & \textbf{+70.9\%} \\
\midrule
\multirow{2}{*}{Ahimsa Score} & Gemini 2.0 Flash & 0.755 & \textbf{0.902} & \textbf{+19.5\%} \\
 & Claude 3.5 Sonnet & 0.772 & \textbf{0.812} & \textbf{+5.2\%} \\
\midrule
\multirow{2}{*}{Helpfulness Score} & Gemini 2.0 Flash & 0.694 & \textbf{0.748} & \textbf{+7.8\%} \\
 & Claude 3.5 Sonnet & 0.595 & \textbf{0.551} & \textbf{-7.4\%} \\
\midrule
\multirow{2}{*}{Dharma Violations} & Gemini 2.0 Flash & 33.0\% & \textbf{4.0\%} & \textbf{-87.9\%} \\
 & Claude 3.5 Sonnet & 34.0\% & \textbf{4.0\%} & \textbf{-88.2\%} \\
\bottomrule
\end{tabular}%
}
\end{table}

The cross-evaluator analysis reveals several key findings. Most importantly, both evaluators confirm ArGen's dramatic improvement in Dharma (scope adherence), with Claude 3.5 Sonnet showing a 70.9\% improvement in Dharma scores and an 88.2\% reduction in violation rates, while Gemini 2.0 Flash shows a 79.1\% improvement in Dharma scores and an 87.9\% reduction in violation rates. Both evaluators consistently validated the relative performance gains achieved by the ArGen framework, with Claude 3.5 Sonnet serving as the primary evaluator for our canonical results. The consistency in Dharma improvements is particularly significant, as this represents the core governance capability that ArGen was designed to enhance.

The evaluators showed divergence in helpfulness assessment, with Claude 3.5 Sonnet indicating modest decline (-7.4\%) while Gemini 2.0 Flash suggested preservation (+7.8\%). This evaluator-specific variation highlights the complexity of helpfulness measurement, but importantly, both evaluators confirmed substantial improvements in the primary alignment objectives of safety (Ahimsa) and domain adherence (Dharma). The overall combined scores showed consistent positive improvements with both evaluators, confirming that ArGen achieves meaningful alignment gains that are robust across different evaluation perspectives.

\paragraph{Helpfulness Preservation Strategy}
For deployment scenarios prioritising helpfulness retention, our comprehensive evaluation identified optimisation strategies that better preserve this capability. The Helpfulness-Optimised model variant (GRPO7 Seed 3, checkpoint-1000) demonstrates ArGen's configurability for application-specific trade-off optimisation, achieving minimal helpfulness degradation (-0.1\%) while maintaining strong alignment improvements. This variant illustrates that ArGen's framework allows for helpfulness-critical applications to maintain user experience quality while achieving principled alignment, demonstrating the system's adaptability to diverse deployment requirements.

\subsubsection{Learning Dynamics}

The training logs (\verb|grpo5_run_summary.txt|) illustrate the auto-regulatory process in action.

% Include the modular training dynamics figure with pgfplots (EMA-smoothed for clarity)
\input{figures/training-dynamics-figure}

As shown in Figure~\ref{fig:training-dynamics}(a) and the training logs, the \verb|Combined Reward| demonstrated a consistent upward trend, starting around 0.66 and stabilizing near 0.82 towards the end of training. The individual principle scores show how the model learned to balance the different objectives:
\begin{itemize}
    \item \textbf{Ahimsa:} Started high ($\sim$0.83) and rapidly converged to and maintained a score above 0.90, indicating the model quickly learned the primary safety constraints.
    \item \textbf{Dharma:} Showed the most significant growth, starting at a low $\sim$0.52 and steadily climbing to $\sim$0.84, demonstrating the direct impact of the scope-checking policy penalties.
    \item \textbf{Helpfulness:} Started at a relatively high $\sim$0.69 and maintained stable performance throughout training, finishing around $\sim$0.71-0.74. This reflects the model's ability to preserve communication capabilities while learning alignment constraints.
\end{itemize}

The training stability is further demonstrated in Figure~\ref{fig:training-dynamics}(b-d). The training loss (Figure~\ref{fig:training-dynamics}(b)) shows stable, low loss throughout the run, indicating that the GRPO process was not destabilized by the complex, multi-objective reward signal. The KL divergence control (Figure~\ref{fig:training-dynamics}(c)) demonstrates controlled policy updates that maintain model capabilities while enabling ethical alignment, and the reward standard deviation (Figure~\ref{fig:training-dynamics}(d)) shows decreasing reward variance, indicating convergence toward consistent principled behavior.

\subsubsection{Qualitative Examples}

Quantitative metrics are substantiated by qualitative improvements in the model's behaviour. To provide concrete insight into these improvements, we present detailed prompt-response comparisons that illustrate the specific behavioural changes achieved through ArGen training. To provide concrete insight into these improvements, we present detailed prompt-response comparisons that illustrate the specific behavioural changes achieved through ArGen training.



% Include qualitative comparison figures
\input{figures/qualitative-comparisons}

The qualitative comparisons in Figures~\ref{fig:dharma-scope-adherence} and~\ref{fig:ahimsa-helpfulness-integration} provide concrete illustrations of the behavioural improvements achieved through ArGen training. These figures demonstrate how the baseline model violates scope boundaries and safety protocols, while MedGuide-AI correctly maintains domain focus, prioritises safety, and integrates appropriate disclaimers. The detailed prompt-response examples that follow further illustrate both scope adherence (Dharma) and safety improvements (Ahimsa) across various scenarios.

\paragraph{Training Dynamics and Principle Evolution}

The training logs reveal how the model progressively learned to balance the three core principles. The Dharma (scope adherence) scores showed the most dramatic improvement, with the Best-Performing Model (as defined in Section~\ref{sec:model-selection}) achieving a 70.9\% improvement over the canonical baseline (0.5640 to 0.9641). This progression directly correlates with the reduction in scope violations from 34\% in the baseline to just 4\% in the Best-Performing Model. The Ahimsa (safety) scores demonstrated steady improvement, with the Best-Performing Model achieving a 5.2\% improvement over baseline (0.7723 to 0.8122). While Helpfulness scores showed a modest decline (-7.4\%, from 0.5952 to 0.5513), this represents a principled trade-off that prioritises safety and domain adherence. These metrics demonstrate that ArGen successfully taught the model to internalise explicit governance policies while maintaining balanced performance across all objectives.

\subsubsection{Impact of Policy Interventions}

An analysis of the evaluation logs reveals the systematic impact of the Python-based policy engine. In 34\% of the benchmark scenarios, the baseline model's responses were classified with scope violations (`S2' or `S3'), incurring substantial penalties through the scope penalty configuration (S0-S1: 1.0, S0-S2: 0.3, S0-S3: 0.0). The ArGen Best-Performing Model reduced this to just 4\% (`S2' or `S3' responses), with 96\% of responses achieving perfect scope adherence (S0).

The detailed prompt-response analysis presented above demonstrates that this improvement represents genuine behavioural change rather than superficial metric optimisation. The model learned to recognise domain boundaries, prioritise safety in medical contexts, and maintain helpfulness within appropriate constraints. This pattern shows that the Policy Model successfully internalised the explicit rules encoded in the \verb|dharma_scope_check| function, demonstrating ArGen's effectiveness in translating governance policies into learned model behaviour.

The training dynamics further reveal that this learning occurred progressively, with the model initially struggling with scope adherence (Dharma scores starting at 0.516) but steadily improving as the policy penalties shaped its behavior. The final convergence to stable, high-performance metrics across all principles demonstrates that ArGen achieves principled alignment without destabilizing the underlying model capabilities.

\subsubsection{Ablation Study: Disentangling Component Contributions}
\label{sec:ablation-study}

To understand the relative contributions of ArGen's reward optimisation and policy enforcement components, we conducted a systematic ablation study comparing three configurations against the Median Seed baseline (GRPO7 Seed 3, Combined Score 0.7825).

\paragraph{Ablation Methodology}
We evaluated three model variants:
\begin{itemize}
    \item \textbf{Full ArGen}: Complete framework with both reward optimisation and policy enforcement
    \item \textbf{Reward-Only}: ArGen with LLM-based reward optimisation but disabled policy penalties
    \item \textbf{Policy-Only}: ArGen with policy enforcement penalties but disabled LLM reward optimisation
\end{itemize}

All ablations were evaluated using both Claude 3.5 Sonnet and Gemini 2.0 Flash to ensure cross-evaluator validation of findings.

\begin{table}[!ht]
\centering
\caption{Ablation Study Results: Component Contribution Analysis}
\label{tab:ablation-results}
\resizebox{\textwidth}{!}{%
\begin{tabular}{@{}lcccc@{}}
\toprule
\textbf{Configuration} & \textbf{Claude Score} & \textbf{Change vs Baseline} & \textbf{Gemini Score} & \textbf{Change vs Baseline} \\
\midrule
Median Seed Baseline & 0.7825 & -- & 0.8599 & -- \\
Reward-Only & 0.7712 & \textbf{-1.4\%} & 0.8550 & \textbf{-0.6\%} \\
Policy-Only & 0.7365 & \textbf{-5.9\%} & 0.7829 & \textbf{-9.0\%} \\
\bottomrule
\end{tabular}
}
\end{table}

\paragraph{Component Analysis Results}
The ablation study reveals several critical insights about ArGen's architecture:

\textbf{Reward Optimisation Robustness}: Reward-only configurations maintain near-baseline performance with minimal degradation (-1.0\% average across evaluators). This suggests that LLM-based reward optimisation provides a robust foundation for alignment, capable of maintaining most performance characteristics even without explicit policy enforcement.

\textbf{Policy Component Criticality}: Policy-only configurations show substantial performance degradation (-7.4\% average), with particularly severe impact on Dharma (domain adherence): -10.8\% (Claude) and -17.1\% (Gemini). This demonstrates that while policy enforcement alone cannot maintain overall performance, it is essential for domain-specific adherence.

\textbf{Dharma Degradation Analysis}: The most striking finding is the severe Dharma degradation in policy-only ablations, while reward-only ablations actually maintain or slightly improve Dharma scores (+0.4\% Claude, -1.6\% Gemini). This suggests that LLM-based reward optimisation is more effective at learning domain boundaries than explicit policy rules alone.

\textbf{Hybrid Architecture Validation}: The superior performance of the full ArGen framework (baseline) compared to both ablations validates the hybrid approach. Neither component alone achieves the balanced performance of the integrated system, demonstrating the synergistic effect of combining LLM-based reward optimisation with policy enforcement.

\paragraph{Cross-Evaluator Validation}
The consistency of findings across Claude 3.5 Sonnet and Gemini 2.0 Flash evaluators strengthens the robustness of these conclusions. Both evaluators confirm:
\begin{itemize}
    \item Reward-only configurations degrade less than policy-only
    \item Policy-only configurations show severe Dharma degradation
    \item Full ArGen achieves optimal balance across all metrics
\end{itemize}

\paragraph{Statistical Significance}
Effect size analysis using Cohen's d reveals:
\begin{itemize}
    \item \textbf{Reward-Only vs Baseline}: Negligible effect (d = -0.11 Claude, -0.05 Gemini)
    \item \textbf{Policy-Only vs Baseline}: Small to medium effect (d = -0.46 Claude, -0.77 Gemini)
    \item \textbf{Largest Impact}: Policy-Only Dharma degradation (d = -1.58 Gemini, Large effect)
\end{itemize}

\paragraph{Implications for ArGen Design}
These findings have important implications for ArGen's architecture and deployment:
\begin{enumerate}
    \item \textbf{Reward optimisation provides robust foundation}: LLM-based evaluation can maintain most alignment properties independently
    \item \textbf{Policy enforcement essential for domain adherence}: Explicit rules remain critical for maintaining operational boundaries
    \item \textbf{Hybrid approach optimal}: The combination of both components achieves superior balanced performance
    \item \textbf{Component prioritisation}: In resource-constrained scenarios, reward optimisation should be prioritised over policy-only approaches
\end{enumerate}
