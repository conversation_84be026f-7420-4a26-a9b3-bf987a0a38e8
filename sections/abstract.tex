This paper introduces \textbf{ArGen (Auto-Regulation of Generative AI systems)}, a framework for aligning Large Language Models (LLMs) with complex sets of configurable, machine-readable rules spanning ethical principles, operational safety protocols, and regulatory compliance standards. Moving beyond just preference-based alignment, ArGen is designed to ensure LLMs adhere to these multifaceted policies through a novel synthesis of principle-based automated reward scoring, Group Relative Policy Optimisation (GRPO), and an Open Policy Agent (OPA) inspired governance layer. This approach provides the technical foundation for achieving and demonstrating compliance with diverse and nuanced governance requirements.

To showcase the framework's capability to operationalize a deeply nuanced and culturally-specific value system, we present an in-depth case study: the development of a medical AI assistant guided by principles from Dharmic ethics (such as Ahimsa and Dharma), as derived from texts like the Bhagavad Gita. This challenging application demonstrates ArGen’s adaptability, achieving a 70.9\% improvement in domain-scope adherence over the baseline. Through our open-source repository, we show that ArGen's methodology offers a path to 'Governable AI'—systems that are technically proficient, ethically robust, and verifiably compliant for safe deployment in diverse global contexts.

  \textit{Keywords}: \textbf{Policy as Code; AI Governance; Custom AI Models; AI Regulation; Group Relative Policy
Optimisation; AI alignment; Dharmic ethics; Bhagavad Gita; Open
Policy Agent; Reinforcement Learning;  AI safety; ethical AI}
