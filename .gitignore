**/training_reports/*
**/training_reports/
**/eval_output/*
**/eval_output/
**/grpo_output/*
**/grpo_output/
**/debug_grpo_output/*
**/debug_grpo_output/
**/fix_grpo_output/*
**/fix_grpo_output/
**/wandb/*
**/wandb/
**/output/*
**/output/
**/logs/*
**/logs/
**/models/*
**/models/
**/checkpoints/*
**/experimental_inputs/*
main.aux
main.log
main.pdf
main.out
main.bbl
main.blg
*.tar
**/arxiv_submission/*

# Environment variables and secrets
.env
.env.local
.env.*.local
!.env.example
.vscode/
