**/wandb/*
**/wandb/
**/output/*
**/output/
**/logs/*
**/logs/
**/models/*
**/models/
.venv/
*.venv/
argen-demo-venv/
**/argen-demo-venv/*
*.venv/
**/argen-demo-venv/
**/debug-logs/*
**/debug-logs/
**/debug-grpo-output/*
**/debug-grpo-output/
**/fix-grpo-output/*
**/fix-grpo-output/
**/grpo-output/*
**/grpo-output/
**/grpo-output-debug/*
**/grpo-output-debug/

# IDE-specific files
.vscode/
.regal/
.idea/

# Python-specific files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Jupyter Notebook
.ipynb_checkpoints

# Virtual environments
venv/
ENV/
env/

# OS-specific files
.DS_Store
Thumbs.db

.env
.env.local
.env.*.local
!.env.example
argen-demo.code-workspace

# Large model files and checkpoints
*.pt
*.pth
*.bin
*.ckpt
*.safetensors
**/checkpoints/
**/checkpoint-*/

# Cache directories
.cache/
**/.cache/
.pytest_cache/
.coverage
htmlcov/

# Temporary files
*.tmp
*.temp
*.swp
*~

# Log files
*.log
*.log.*